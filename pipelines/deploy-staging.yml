trigger:
  branches:
    include:
      - main

pool:
  vmImage: ubuntu-latest

variables:
  - group: staging

stages:
  - stage: SetupEnvironment
    displayName: "Setup environment with python, poetry and dependencies"
    jobs:
      - deployment: SetupEnvironment
        displayName: "Setup Python, Poetry and App Package"
        environment:
          name: AZU382ITN18001
          resourceType: VirtualMachine
          tags: staging
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  fetchDepth: 1

                - task: Bash@3
                  displayName: "Environment setup & Run DB Migration with Poetry"
                  inputs:
                    targetType: 'inline'
                    script: |
                      cd $(Build.SourcesDirectory)

                      echo "📦 Starting prepare environment script..."
                      bash ./prepare-env.sh

                      echo "📦 Starting Alembic migration..."
                      if [ ! -f migrations/alembic.ini ]; then
                          echo "❌ 'alembic.ini' not found in migrations/ directory"
                          exit 1
                      fi

                      echo "🔍 Checking Alembic status and pending migrations..."
                      if ! poetry run alembic -c migrations/alembic.ini current > /dev/null 2>&1; then
                          echo "❌ Failed to get current revision from Alembic. Database may be unreachable or FLASK_APP misconfigured."
                          echo "❌ Aborting migration."
                          # exit 1
                      fi

                      if ! poetry run alembic -c migrations/alembic.ini heads > /dev/null 2>&1; then
                          echo "❌ Failed to get heads revision from Alembic. Please check Alembic configuration."
                          # exit 1
                      fi

                      current_rev=$(poetry run alembic -c migrations/alembic.ini current | grep -Eo '^[a-f0-9]{12}')
                      latest_rev=$(poetry run alembic -c migrations/alembic.ini heads | grep -Eo '^[a-f0-9]{12}')

                      if [ -z "$current_rev" ] || [ -z "$latest_rev" ]; then
                          echo "❌ Revision values are empty despite Alembic being available. Aborting pipeline."
                          # exit 1
                      fi

                      if [ "$current_rev" != "$latest_rev" ]; then
                          echo "📦 Migration required. Current revision: $current_rev → Latest revision: $latest_rev"
                          poetry run alembic -c migrations/alembic.ini upgrade head || {
                              echo "⚠️ Alembic upgrade failed."
                              # exit 1
                          }
                      else
                          echo "✅ No migration required. Current revision matches latest: $current_rev"
                      fi


                      echo "📜 Exporting requirements.txt from pyproject.toml using Poetry"
                      poetry self add poetry-plugin-export || true
                      poetry export -f requirements.txt --output requirements.txt --without-hashes
                      echo "✅ File requirements.txt created.."

                      echo "🧹 Cleaning up non-essential files and folders..."
                      find . -type d -name "__pycache__" -exec rm -rf {} +
                      find . -type f -name "*.pyc" -delete
                      find . -type f -name "*.pyo" -delete
                      rm -rf docs .git .venv tests pipelines seeds .env.example CHANGELOG.md .pre-commit-config.yaml README.md .devcontainer.json .mypy_cache docker-entrypoint.sh Dockerfile .dockerignore .gitignore pyproject.toml poetry.lock
                      find . -type d -name "migrations" -exec rm -rf {} +
                      echo "✅ Cleanup complete."

                      echo "📂 pyproject.toml: $(find . -name 'pyproject.toml' -print -quit || echo '❌ Non trovato')"
                      echo "📂 poetry.lock:    $(find . -name 'poetry.lock' -print -quit || echo '❌ Non trovato')"
                      echo "📂 requirements.txt: $(find . -name 'requirements.txt' -print -quit || echo '❌ Non trovato')"

                      echo "📦 Creating cleaned zip archive..."
                      mkdir -p $(Pipeline.Workspace)/drop
                      zip -r $(Pipeline.Workspace)/drop/application.zip . -x "*/.venv/*"

                - publish: $(Pipeline.Workspace)/drop/application.zip
                  artifact: drop

  - stage: DeployStage
    displayName: "Deploy to App Service"
    dependsOn: SetupEnvironment
    condition: succeeded()
    jobs:
      - deployment: DeployApp
        displayName: "Deploy Application"
        environment:
          name: AZU382ITN18001
          resourceType: VirtualMachine
          tags: staging
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: drop

                - task: AzureRmWebAppDeployment@4
                  displayName: "Deploy to App Service"
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: 'Azure-UMI-SpartaxNPD-Deployment'
                    appType: 'webAppLinux'
                    WebAppName: 'spartaxapitest'
                    packageForLinux: '$(Pipeline.Workspace)/drop/application.zip'
