trigger:
  branches:
    include:
      - stable

pool:
  vmImage: ubuntu-latest

variables:
  - group: production

stages:
- stage: BuildAndPackage
  jobs:
    - job: build
      displayName: "Build, migrate, zip"
      steps:
        - checkout: self
          fetchDepth: 1

        - bash: |
            set -e
            export PATH="$HOME/.local/bin:$PATH"
            if ! command -v poetry >/dev/null; then
              echo "📥 Installing Poetry with same version of Appservice Oryx"
              curl -sSL https://install.python-poetry.org | POETRY_VERSION=1.5.1 python3 -
            fi
            poetry --version
          displayName: "Ensure Poetry v.1.5.1"

        - bash: |
            set -e
            export PATH="$HOME/.local/bin:$PATH"
            poetry config virtualenvs.in-project true
            poetry lock --no-update
            poetry install --no-root --only main --no-ansi
          displayName: "Poetry install (runtime deps)"

        - bash: |
            set -e
            find . -type d -name "__pycache__" -exec rm -rf {} +
            find . -type f -name "*.py[co]" -delete
            rm -rf docs tests pipelines seeds .env.example .venv \
                   CHANGELOG.md README.md .devcontainer.json .mypy_cache \
                   .pre-commit-config.yaml .git
            mkdir -p $(Pipeline.Workspace)/drop
            zip -r $(Pipeline.Workspace)/drop/application.zip .
          displayName: "Create ZIP"

        - publish: $(Pipeline.Workspace)/drop/application.zip
          artifact: drop

- stage: Deploy
  dependsOn: BuildAndPackage
  jobs:
    - deployment: deploy
      environment: AZU382ITN18001
      strategy:
        runOnce:
          deploy:
            steps:
              - download: current
                artifact: drop

              - task: AzureRmWebAppDeployment@4
                displayName: "Deploy ZIP to App Service"
                inputs:
                  ConnectionType: "AzureRM"
                  azureSubscription: "Azure-UMI-SpartaxPD-Deployment"
                  appType: "webAppLinux"
                  WebAppName: "spartaxapiprod"
                  packageForLinux: "$(Pipeline.Workspace)/drop/application.zip"
