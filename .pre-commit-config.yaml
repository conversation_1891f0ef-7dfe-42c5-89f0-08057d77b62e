repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-docstring-first
      - id: check-merge-conflict
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: https://github.com/pre-commit/mirrors-isort
    rev: v5.10.1
    hooks:
      - id: isort
  - repo: https://github.com/Lucas-C/pre-commit-hooks-bandit
    rev: v1.0.6
    hooks:
      - id: python-bandit-vulnerability-check
        args: [--skip, "B101", --recursive, clumper]
  # - repo: local
  #   hooks:
  #     - id: pytest-check
  #       name: pytest-check
  #       entry: pytest
  #       language: system
  #       pass_filenames: false
  #       always_run: false
