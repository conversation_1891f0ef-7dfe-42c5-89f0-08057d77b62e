# def test_balance_search(test_client):

#     response = test_client.get("/balances/05113870967")
#     assert response.status_code == 200
#     assert b"in_db" in response.data
#     assert b"vat_code" in response.data


# def test_input_format_wrong_vat_code(test_client):
#     response = test_client.get("/balances/slkhgoi")
#     assert response.status_code == 422
#     assert b"message" in response.data


# def test_input_wrong_content_vat_or_product_id(test_client):
#     response = test_client.get("/balances/01234567890/012345678")
#     assert response.status_code == 404
#     assert b"message" in response.data


# def test_single_balance(test_client):
#     response = test_client.get("/balances/05113870967/130973083")
#     assert response.status_code == 200
#     assert b"content" in response.data
#     assert b"vat_code" in response.data
