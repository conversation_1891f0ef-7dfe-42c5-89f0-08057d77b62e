def test_single_company(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/companies/<string:vat_code>' page is requested (GET)
    THEN check that the response is valid
    """
    response = test_client.get("/companies/01551260506")
    assert response.status_code == 200
    assert b"id" in response.data
    assert b"vat_code" in response.data


def test_wrong_format_vat_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/companies/<string:vat_code>' page is requested with wrong format of vat_code (GET)
    THEN check that the response returns a 422 error with an error message
    """
    response = test_client.get("/companies/01551506")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_content_vat_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/companies/<string:vat_code>' page is requested with wrong content of vat_code (GET)
    THEN check that the response returns a 422 error with an error message
    """
    response = test_client.get("/companies/01234567890")
    assert response.status_code == 422
    assert b"message" in response.data
