# def test_wrong_input_format_tax_code(test_client):
#     response = test_client.get("/company_card/0123456")
#     assert response.status_code == 404
#     assert b"message" in response.data


# def test_wrong_input_product_id(test_client):
#     response = test_client.get("/company_card/01234567890/kldsfoao")
#     assert response.status_code == 404
#     assert b"message" in response.data


# def test_wrong_input_content_tax_code(test_client):
#     response = test_client.get("/company_card/01234567890/SilverCribis")
#     assert response.status_code == 404
#     assert b"message" in response.data
