# def test_single_visura(test_client):
#     """
#     GIVEN a Flask application configured for testing
#     WHEN the '/visura/tax_code=<string:tax_code>' page is requested (GET)
#     THEN check that the response is valid
#     """
#     response = test_client.get("/visura/tax_code=01551260506")
#     assert response.status_code == 200
#     assert b"ProductID" in response.data
#     assert b"TAXCode" in response.data


def test_wrong_format_vat_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/visura/tax_code=<string:tax_code>' page is requested with wrong format of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/visura/tax_code=01551506")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_content_tax_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/visura/tax_code=<string:tax_code>' page is requested with wrong content of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/visura/tax_code=01234567890")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_product_id(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/visura/tax_code=<string:tax_code>, product_id=<string:product_id>' page is requested with wrong product_id (GET)
    THEN check that the response is
    """
    response = test_client.get("/visura/tax_code=01551260506,product_id=AKSLDJF")
    assert response.status_code == 422
    assert b"message" in response.data
