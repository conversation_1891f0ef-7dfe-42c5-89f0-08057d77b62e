# def test_single_report(test_client):

#     response = test_client.get("/reports/05113870967/RptExpert")
#     assert response.status_code == 200
#     assert b"content" in response.data
#     assert b"vat_code" in response.data


def test_wrong_format_input_vat_code(test_client):
    response = test_client.get("/reports/050967/RptExpert")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_input_product_id_code(test_client):
    response = test_client.get("/reports/05113870967/RptExp")
    assert response.status_code == 422
    assert b"message" in response.data


def test_invalid_vat_code(test_client):
    response = test_client.get("/reports/01234567890/RptExpert")
    assert response.status_code == 422
    assert b"message" in response.data
