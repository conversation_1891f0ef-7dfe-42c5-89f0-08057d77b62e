# def test_financial_statement(test_client):
#     """
#     GIVEN a Flask application configured for testing
#     WHEN the '/financial_statement/<string:vat_code>' page is requested (GET)
#     THEN check that the response is valid
#     """
#     response = test_client.get("/financial_statements/vat_code=05113870967")
#     assert response.status_code == 200
#     assert b"id" in response.data
#     assert b"vat_code" in response.data


def test_wrong_format_vat_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/financial_statements/<string:vat_code>' page is requested with wrong format of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/financial_statements/vat_code=01551260")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_content_vat_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/financial_statement/<string:vat_code>' page is requested with wrong content of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/legal_procedures_deed/vat_code=01234567890")
    assert response.status_code == 422
    assert b"message" in response.data
