# def test_representative_card(test_client):
#     """
#     GIVEN a Flask application configured for testing
#     WHEN the '/representative_card/<string:tax_code>/<string:product_id>' page is requested (GET)
#     THEN check that the response is valid
#     """
#     response = test_client.get("/representative_card/DLFRCR87C18L157Y/SKS")
#     assert response.status_code == 200
#     assert b"id" in response.data
#     assert b"TAXCode" in response.data


def test_wrong_format_tax_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/representative_card/<string:tax_code>/<string:product_id>' \
        page is requested with wrong format of vat_code (GET)
    THEN check that the response is 422
    """
    response = test_client.get("/representative_card/DLFRCC18L157Y/SKS")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_content_tax_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/shares/<string:vat_code>' page is requested with wrong content of vat_code (GET)
    THEN check that the response is 422
    """
    response = test_client.get("/representative_card/ZZZZZZ11Z11L157Y/SKS")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_product_id(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/representative_card/<string:tax_code>/<string:product_id>' page is requested with wrong format of product_id (GET)
    THEN check that the response is 422
    """
    response = test_client.get("/representative_card/DLFRCR87C18L157Y/ZZZ")
    assert response.status_code == 422
    assert b"message" in response.data
