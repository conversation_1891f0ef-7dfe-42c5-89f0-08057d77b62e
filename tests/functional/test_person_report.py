# def test_single_report(test_client):

#     response = test_client.get("/person_report/DLFRCR87C18L157Y/RptStandard")
#     assert response.status_code == 200
#     assert b"Content" in response.data
#     assert b"TAXCode" in response.data


def test_wrong_format_input_tax_code(test_client):
    response = test_client.get("/person_report/DLFRCR818L157Y/RptStandard")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_input_product_id_code(test_client):
    response = test_client.get("/reports/DLFRCR87C18L157Y/RptExp")
    assert response.status_code == 422
    assert b"message" in response.data


def test_invalid_tax_code(test_client):
    response = test_client.get("/reports/ZZZZZZ11Z11L157Y/RptStandard")
    assert response.status_code == 422
    assert b"message" in response.data
