# In your tests/test_mail_resource.py
import pytest
from flask import Flask
from flask.testing import FlaskClient
from flask_mail import Mail
from flask_restful import Api

from app.resources.mail import MailResource


@pytest.fixture
def app():
    app = Flask(__name__)
    app.config["TESTING"] = True
    app.config["MAIL_DEFAULT_SENDER"] = "<EMAIL>"
    app.config["MAIL_SERVER"] = "appmail.atrema.deloitte.com"
    app.config["MAIL_PORT"] = 25

    mail = Mail(app)
    api = Api(app)
    api.add_resource(MailResource, "/mail")

    app.mail = mail
    app.testing_client = app.test_client()

    yield app


def test_send_email_success(app: Flask, client: FlaskClient):
    response = client.post(
        "/mail",
        json={
            "email": "<EMAIL>",
            "subject": "Test Subject",
            "message": "Test Message Content",
        },
    )

    assert response.status_code == 200
    assert response.json == {"message": "Email sent successfully"}


def test_send_email_missing_params(app: Flask, client: FlaskClient):
    response = client.post(
        "/mail", json={"subject": "Test Subject", "message": "Test Message Content"}
    )

    assert response.status_code == 400
    assert response.json == {
        "message": {"email": "Email address to send to is required."}
    }
