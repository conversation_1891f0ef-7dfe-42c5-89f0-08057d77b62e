# def test_single_person(test_client):
#     """
#     GIVEN a Flask application configured for testing
#     WHEN the '/people/<string:tax_code>' page is requested (GET)
#     THEN check that the response is valid
#     """
#     response = test_client.get("/people/DLFRCR87C18L157Y")
#     assert response.status_code == 200
#     assert b"id" in response.data
#     assert b"tax_code" in response.data


def test_wrong_format_tax_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/people/<string:tax_code>' page is requested with wrong format of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/people/DLFRCR87C18")
    assert response.status_code == 422
    assert b"message" in response.data


def test_wrong_content_tax_code(test_client):
    """
    GIVEN a Flask application configured for testing
    WHEN the '/people/<string:tax_code>' page is requested with wrong content of vat_code (GET)
    THEN check that the response is
    """
    response = test_client.get("/people/ZZZZZZ11Z11L157Y")
    assert response.status_code == 422
    assert b"message" in response.data
