def test_new_person(new_person):
    """
    GIVEN a Person model
    WHEN a new Person is created
    THEN check the different values are assigned correctly
    """

    assert new_person.ranking == 80.97
    assert new_person.surname == "Test Surname"
    assert new_person.name == "Test Name"
    assert new_person.tax_code == "Test TAXCode"


# def test_new_balance(new_balance):
#     """
#     GIVEN a Balance model
#     WHEN a new Balance is created
#     THEN check the different values are assigned correctly
#     """

#     assert new_balance.chiave_doc_fisico == "Test Chiave"
#     assert new_balance.desc_atto == "Test Desc Atto"


# def test_new_report(new_report):
#     """
#     GIVEN a Report model
#     WHEN a new Report is created
#     THEN check the different values are assigned correctly
#     """

#     assert new_report.id == "Test Document ID"
#     assert new_report.product_id == "Test Product ID"


# def test_new_company(new_company):
#     """
#     GIVEN a Company model
#     WHEN a new Company is created
#     THEN check the different values are assigned correctly
#     """

#     assert new_company.name == "Test Nome"
#     assert new_company.ranking == 67.69


# def test_new_visura(new_visura):
#     """
#     GIVEN a Visura model
#     WHEN a new Visura is created
#     THEN check the different values are assigned correctly
#     """

#     assert new_visura.tax_code == "Test Tax Code"
