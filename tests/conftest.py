"""
Pytest configuration and fixtures for the Spartax API application.
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# Carica le variabili d'ambiente dal file .env prima di qualsiasi importazione
from dotenv import load_dotenv

env_path = Path(__file__).parent.parent / ".env.test"
if env_path.exists():
    print(f"Caricando variabili d'ambiente da {env_path.absolute()}")
    load_dotenv(env_path)
else:
    print(f"ATTENZIONE: File .env non trovato in {env_path.absolute()}")
    print("I test potrebbero fallire se mancano variabili d'ambiente necessarie.")

import pytest

# Aggiunge la root del progetto al PYTHONPATH per consentire 'import app'
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# pylint: disable=wrong-import-position
from app.extensions import db
from app.main import app
from app.models.company import Company
from app.models.person import Person

# pylint: enable=wrong-import-position


@pytest.fixture(scope="module")
def app_fixture():
    """
    Configura e restituisce un'istanza Flask per i test.

    - Abilita la modalità TESTING
    - Utilizza un database SQLite in-memory
    - Crea tutte le tabelle prima dei test e le elimina alla fine
    """
    app.config["TESTING"] = True
    app.config["SQLALCHEMY_DATABASE_URI"] = "sqlite:///:memory:"
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture(scope="module")
def test_client(app_fixture):
    """
    Fornisce un test client per l'app Flask configurata da `app_fixture`.
    """
    with app_fixture.test_client() as client:
        yield client


@pytest.fixture(scope="module")
def new_person():
    """
    Crea e persiste un'istanza `Person` nel database di test.

    Ritorna l'oggetto `Person` appena creato.
    """
    person = Person(
        ranking=80.97,
        surname="Test Surname",
        name="Test Name",
        tax_code="TESTTAXCODE01",
        birth_date=datetime(1900, 1, 1),
        birth_town="Test BirthTown",
        birth_province="TP",
        birth_province_description="Test Province Description",
        gender="M",
        address="Test Address",
        town="Test Town",
        village="Test Village",
        province="Test Province",
        zip="00000",
        country="Test Country",
        has_roles="user",
        is_sole_trader="yes",
        is_shareholder="no",
    )
    db.session.add(person)
    db.session.commit()
    return person


@pytest.fixture(scope="module")
def new_company():
    """
    Crea e persiste un'istanza `Company` nel database di test.

    Ritorna l'oggetto `Company` appena creato.
    """
    company = Company(
        vat_code="*************",
        tax_code="12345678901",
        ranking=67.69,
        name="Test Company Name",
        crif_number="CRIF123",
        activity_description="Test Activity Description",
        address="Via Test 1",
        town="Test Town",
        province_code="TP",
        province_description="Test Province",
        zip_code="00000",
        region="Test Region",
        cciaa="TESTCCIAA",
        rea="REA123456",
        legal_form_code="LF",
        legal_form_description="Test Legal Form",
        unit_type_code="UT",
        unit_type_description="Test Unit Type",
        company_status_code="ACTIVE",
        company_status_description="Active Company",
        activity_status_code="OK",
        activity_status_code_description="Operational",
        flag_out_of_business=False,
        flag_news_available=False,
        flag_payment_info_available=False,
    )
    db.session.add(company)
    db.session.commit()
    return company
