"""
This module serves as the entry point for the Flask application, configuring
and initializing the core components of the API.

The main tasks handled by this module include:
- Loading environment variables from a `.env` file.
- Configuring and initializing Flask extensions such as SQLAlchemy, Flask-Migrate, Flask-Limiter,
and Flask-Mail.
- Setting up Cross-Origin Resource Sharing (CORS) to control how the front-end interacts
with the API.
- Registering all API resources and routes.
- Applying security-related HTTP headers to each response.
- Handling rate-limiting to protect the API from excessive requests.

"""

import os
import sys
from http import HTTPStatus

from flask import Flask, abort, request
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_migrate import Migrate
from flask_restful import Api
from opencensus.ext.azure.trace_exporter import AzureExporter
from opencensus.ext.flask.flask_middleware import FlaskMiddleware
from opencensus.trace.samplers import ProbabilitySampler

from app.extensions import db, email, seeder, supplier
from app.resources.balance import BalanceListResource, BalanceResource
from app.resources.coc import CocResource
from app.resources.company import CompanyResource
from app.resources.company_card import CompanyCardResource
from app.resources.document import (
    DocumentListResource,
    DocumentResource,
    DocumentTypesResource,
)
from app.resources.financial_statement import (
    FinancialStatementListResource,
    FinancialStatementResource,
)
from app.resources.health import HealthResource
from app.resources.legal_procedures_deed import LegalProceduresDeedResource
from app.resources.mail import MailResource
from app.resources.person import PersonResource
from app.resources.person_report import PersonReportResource
from app.resources.rebuy_document import Rebuy
from app.resources.report import ReportResource
from app.resources.representative_card import RepresentativeCardResource
from app.resources.request import RequestDelete, RequestGet, RequestPost, RequestPut
from app.resources.shareholders import ShareholdersResource
from app.resources.shares import SharesResource
from app.resources.stats import StatsResource
from app.resources.user import UserAccess
from app.resources.visura import VisuraResource
from app.routes.home import home


def register_resources(app) -> None:
    """
    Registers all the API resources with their respective endpoints.

    Args:
        api (Api): The Flask-RESTful Api instance to register resources with.

    Resources:
        - CompanyResource: Handles company data.
        - CompanyCardResource: Manages company card information.
        - ReportResource: Provides report data.
        - BalanceListResource: Lists all balances for a company.
        - BalanceResource: Manages specific balance document for a company.
        - FinancialStatementListResource: Lists all financial statements for a company.
        - FinancialStatementResource: Manages specific financial statement document.
        - ShareholdersResource: Handles shareholders information.
        - SharesResource: Manages shares data.
        - VisuraResource: Provides visura documents.
        - PersonResource: Handles person data.
        - PersonReportResource: Provides person report documents.
        - RepresentativeCardResource: Manages representative card documents.
        - LegalProceduresDeedResource: Handles legal procedures deed data.
        - DocumentResource: Manages specific documents.
        - DocumentListResource: Lists all documents.
        - DocumentTypesResource: Provides document types for an entity.
        - UserAccess: Manages user access and authentication.
        - MailResource: Handles mail operations.
        - RequestPut: Handles PUT requests for a specific entity.
        - RequestDelete: Handles DELETE requests for a specific request.
        - RequestPost: Handles POST requests for a specific request
        - RequestGet: Retrieves a list of all requests.
        - CocResource: Manages CoC data.
        - Rebuy: Handles rebuy operations for a document.

    Returns:
        None
    """
    api = Api(app)

    # Company resources
    api.add_resource(CompanyResource, "/companies/<string:vat_code>")

    # Company card resources
    api.add_resource(CompanyCardResource, "/company_card/<uuid:company_id>")

    # Reports resources
    api.add_resource(
        ReportResource, "/reports/<uuid:company_id>/<string:document_type>"
    )

    # Balances resources
    api.add_resource(BalanceListResource, "/balances/<uuid:company_id>")
    api.add_resource(
        BalanceResource, "/balance/<uuid:company_id>/document/<uuid:document_id>"
    )

    # Financial statements resources
    api.add_resource(
        FinancialStatementListResource, "/financial_statements/<uuid:company_id>"
    )
    api.add_resource(
        FinancialStatementResource,
        "/financial_statement/<uuid:company_id>/document/<uuid:document_id>",
    )

    # Shareholders resources
    api.add_resource(ShareholdersResource, "/shareholders/<uuid:company_id>")

    # Shares resources
    api.add_resource(SharesResource, "/shares/<uuid:company_id>")

    # Visure resources
    api.add_resource(
        VisuraResource,
        "/visura/<uuid:company_id>/<string:document_type>",
    )

    # Person resources
    api.add_resource(PersonResource, "/people/<string:tax_code>")

    # Person reports resources
    api.add_resource(
        PersonReportResource, "/person_report/<uuid:person_id>/<string:document_type>"
    )

    # Representative card resources
    api.add_resource(
        RepresentativeCardResource,
        "/representative_card/<uuid:person_id>/<string:document_type>",
    )

    # Legal procedures resources
    api.add_resource(
        LegalProceduresDeedResource, "/legal_procedures_deed/<uuid:company_id>"
    )

    # Documents resources
    api.add_resource(DocumentResource, "/document/<uuid:document_id>")
    api.add_resource(DocumentListResource, "/documents")
    api.add_resource(
        DocumentTypesResource, "/<string:entity_type>/<uuid:entity_id>/status"
    )

    # User resources
    api.add_resource(UserAccess, "/user")

    # Mail resources
    api.add_resource(MailResource, "/mail")

    # Request
    api.add_resource(RequestPut, "/request/<string:entity_type>/<uuid:entity_id>")
    api.add_resource(RequestDelete, "/request/<uuid:request_id>")
    api.add_resource(RequestPost, "/request/<uuid:request_id>")
    api.add_resource(RequestGet, "/requests")

    # Rebuy
    api.add_resource(Rebuy, "/rebuy/<uuid:document_id>")

    # Coc
    api.add_resource(CocResource, "/coc")

    # Health endpoint for application status health check
    api.add_resource(HealthResource, "/health")

    # Stats endpoint to check usage data
    api.add_resource(StatsResource, "/stats/<string:entity>")


def register_extensions(app) -> None:
    """
    Registers and initializes all the extensions with the Flask application instance.

    Args:
        app (Flask): The Flask application instance to register extensions with.

    Extensions:
        - Database: Initializes the SQLAlchemy database extension and migration support.
        - Seeder: Initializes the database seeder extension.
        - Supplier: Initializes the external data supplier extension with certificate validation.
        - Limiter: Initializes the rate limiter extension.
        - Cache: Initializes the cache client (Redis).
        - Email: Configures and initializes the email extension.

    Returns:
        None
    """
    # Database
    db.init_app(app)
    Migrate(app, db)
    # Database seeder
    seeder.init_app(app, db)
    # Supplier
    supplier.init_app(app)
    # Cache
    # Avoid initializing the cache when workin on db
    if not any(
        cmd in sys.argv
        for cmd in [
            "alembic",
            "db",
            "migrate",
            "upgrade",
            "downgrade",
            "current",
            "history",
            "revision",
            "init",
        ]
    ):
        from app.cache import cache

        app.cache = cache
    else:
        app.cache = None

    # Limiter
    limiter.init_app(app)
    # Email
    app.config["MAIL_DEFAULT_SENDER"] = os.getenv("MAIL_DEFAULT_SENDER")
    app.config["MAIL_PORT"] = os.getenv("MAIL_PORT")
    app.config["MAIL_SERVER"] = os.getenv("MAIL_SERVER")
    email.init_app(app)


if os.getenv("WEBSITE_SITE_NAME") is None:
    try:
        from dotenv import load_dotenv

        load_dotenv()
    except ImportError:
        pass

app = Flask(__name__)

client_url = (
    os.getenv("CLIENT_URL")
    if os.getenv("WEBSITE_HOSTNAME")
    else os.getenv("CLIENT_URL_LOC")
)
storage_url = os.getenv("AZURE_STORAGE_URL")
coc_provider_url = os.getenv("COC_PROVIDER_URL")

app.config["SQLALCHEMY_DATABASE_URI"] = os.getenv(
    "SQLALCHEMY_DATABASE_URI",
)
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = os.getenv(
    "SQLALCHEMY_TRACK_MODIFICATIONS"
)


def default_error_responder():
    """
    Generates a default response for rate-limited requests.

    This function is used by the rate-limiter to return a standardized error message when
    a client exceeds the allowed number of requests within a fixed window of time.
    The response includes an HTTP status code indicating that the client has made too many requests,
    along with a simple error message.

    The limiting strategy employed by the rate-limiter is the "fixed window" strategy,
    meaning requests are counted within a fixed time frame, and the limit resets at the start
    of the next time frame.

    Returns:
        tuple: A tuple containing the error message string and the corresponding HTTP status code
        (HTTP 429 Too Many Requests).
    """
    http_status = HTTPStatus.TOO_MANY_REQUESTS
    return ("Too many requests!", http_status)


limiter = Limiter(
    get_remote_address,
    app=app,
    default_limits=["150 per hour"],
    storage_uri="memory://",
)


@app.before_request
def prevent_request_smuggling():
    """
    Middleware function to prevent HTTP request smuggling attacks.

    This function intercepts incoming requests and performs two primary checks:

    1. It blocks any request that includes the 'Transfer-Encoding: chunked' header,
    as this header can be exploited in HTTP request smuggling attacks. By rejecting
    requests with 'Transfer-Encoding: chunked', we limit the server's exposure to
    inconsistencies in header parsing across different components of the application stack.

    2. It ensures that only one of either 'Content-Length' or 'Transfer-Encoding' headers is present
    in the request. Request smuggling attacks often exploit the ambiguity caused by both headers
    being used simultaneously.
    If both are detected, the request is aborted to avoid potential risks.

    Raises:
        werkzeug.exceptions.HTTPException: Returns a 400 Bad Request response if either
        'Transfer-Encoding: chunked' is present or if both 'Content-Length' and 'Transfer-Encoding'
        headers are detected in a single request.

    Usage:
        This function should be registered with Flask as a `before_request` handler to
        intercept and examine all incoming requests.

    Example:
        @app.before_request
        def prevent_request_smuggling():
            # Implementation here

    """
    if request.headers.get("Transfer-Encoding") == "chunked":
        abort(400, "Transfer-Encoding: chunked not supported")

    if request.headers.get("Content-Length") and request.headers.get(
        "Transfer-Encoding"
    ):
        abort(400, "You cannot have both Content-Length and Transfer-Encoding")


@app.route("/<path:path>", methods=["OPTIONS"])
def handle_preflight():
    """
    Handles preflight CORS requests.
    """
    response = Flask.response_class()
    response.headers["Access-Control-Allow-Origin"] = client_url
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = (
        "Accept, Accept-Encoding, Authorization, coc, Content-Type, Origin, Options, id"
    )
    return response


@app.after_request
def add_security_headers(response):
    """
    Adds security-related HTTP headers to each response to enhance the security of the application.

    Args:
        response (Response): The Flask response object that will be sent to the client.

    Returns:
        Response: The modified response object with added security headers.
    """
    # Security headers
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Headers"] = (
        "Accept,Accept-Encoding,Accept-Language,"
        "Access-Control-Allow-Origin,Access-Control-Allow-Credentials,"
        "assignable,Authorization,coc,Connection,Content-Type,Dnt,Options,"
        "Origin,Sec-Ch-Ua,Sec-Ch-Ua-Mobile,Sec-Ch-Ua-Platform,"
        "Sec-Fetch-Dest,Sec-Fetch-Mode,Sec-Fetch-Site,User-Agent,Request,role,id"
    )
    response.headers["Access-Control-Allow-Origin"] = client_url
    response.headers["Access-Control-Allow-Methods"] = "DELETE, GET, OPTIONS, POST, PUT, PATCH"  # fmt: skip
    response.headers["Cache-Control"] = "no-store"
    response.headers["Content-Security-Policy"] = (
        f"default-src 'none'; "
        f"script-src 'self' {client_url}; "
        f"connect-src 'self' {client_url} {storage_url}; "
        f"img-src 'self' data:; "
        f"style-src 'self'; "
        f"font-src 'self' data:; "
        f"object-src 'none'; "
        f"base-uri 'none'; "
        f"form-action 'self'; "
        f"frame-ancestors 'self'; "
        f"report-uri /csp-violation-report-endpoint/"
    )
    response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"
    response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
    response.headers["Cross-Origin-Resource-Policy"] = "same-origin"
    response.headers["Access-Control-Max-Age"] = "600"
    response.headers["Referrer-Policy"] = "no-referrer"
    response.headers["Strict-Transport-Security"] = (
        "max-age=31536000; includeSubDomains; preload"
        if request.is_secure
        else "max-age=0"
    )
    response.headers["Transfer-Encoding"] = "identity"
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
    response.headers["X-XSS-Protection"] = "1; mode=block"

    # Coc endpoint headers
    if request.path == "/coc":
        if request.method == "GET":
            response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = (
                "Accept, Authorization, Content-Type, Origin, Options"
            )
        elif request.method == "POST":
            response.headers["Access-Control-Allow-Origin"] = coc_provider_url
            response.headers["Access-Control-Allow-Credentials"] = "false"
            response.headers["Access-Control-Allow-Methods"] = "POST"
            response.headers["Access-Control-Allow-Headers"] = (
                "Accept, Authorization, Content-Type, Origin, Options"
            )
            response.headers["Access-Control-Max-Age"] = "43200"

    # User endpoint headers
    if request.path == "/user":
        # response.headers["Access-Control-Allow-Credentials"] = "false"
        response.headers["Access-Control-Allow-Headers"] = "coc, id"
        response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"

    return response


# Register extensions and resources
register_extensions(app)
register_resources(app)

app.add_url_rule("/", view_func=home)

# Appinsights log and trace middleware
middleware = FlaskMiddleware(
    app,
    exporter=AzureExporter(
        connection_string=os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING")
    ),
    sampler=ProbabilitySampler(1.0),
)
