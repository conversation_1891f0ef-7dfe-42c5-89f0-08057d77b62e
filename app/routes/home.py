"""
This module provides the HomeResource class, which defines a Flask-RESTful API resource
that serves the content of the project's README.md file and checks the status of the
database connection. It returns the documentation content and a health status message
in HTML format.

Dependencies:
    - flask_restful: For creating the RESTful API resource.
    - flask: For handling the HTTP request and response.
    - markdown: For converting the markdown content of the README.md file into HTML.
    - app.extensions: Provides the database connection used to verify the application's
      connection to the database.

Classes:
    HomeResource: A Flask-RESTful resource that provides the content of the README.md
    file and checks the database connection status.
"""

import os
from http import HTTPStatus

import markdown
from flask import render_template

from app.extensions import db


def home():
    """
    HomeResource is a Flask-RESTful API resource that provides the application's
    homepage content.

    This resource reads the README.md file, converts its content to HTML, and returns
    it along with a message indicating the application's health and database connection status.

    Methods:
        get(): Handles GET requests and returns the README.md content and database status.
    """
    base_dir = os.path.abspath(os.path.dirname(__file__))
    readme_path = os.path.join(base_dir, "..", "..", "README.md")

    with open(readme_path, "r", encoding="utf-8") as file:
        content = file.read()
        html_content = markdown.markdown(content)
        html_content = html_content.replace('href="docs/', 'href="/api/docs/')

        try:
            db_connection = db.engine.connect().connection.is_valid
            data = "Database connection is not active"
            status = HTTPStatus.SERVICE_UNAVAILABLE
            if db_connection:
                return render_template("home.html", html_content=html_content)
            else:
                return (data, status)
        except Exception as exc:
            data = "Database connection is not active"
            status = HTTPStatus.SERVICE_UNAVAILABLE
            raise ConnectionError from exc
