"""
Flask extension for managing external data supplier connections.

This module provides a Flask extension that manages connections to external data supplier APIs,
handles certificate validation, and provides a fail-fast mechanism to prevent Flask from starting
if the supplier configuration is invalid.

Classes:
    SupplierExtension: Flask extension for managing supplier connections
    SupplierClient: Individual supplier client for specific services
"""

import os
import sys
from http import HTTPStatus
from typing import Optional, Union

import requests
from flask import Flask
from zeep import Client, Settings

from app.config import logger


class SupplierClient:
    """
    Individual supplier client for a specific service.

    This class handles the connection to a specific supplier service endpoint
    and manages the WSDL client creation.
    """

    def __init__(
        self, service: str, base_url: str, service_url: str, settings: Settings
    ):
        """
        Initialize a supplier client for a specific service.

        Args:
            service: The service name
            base_url: The base URL for the supplier
            service_url: The specific service endpoint URL
            settings: Zeep client settings
        """
        self.service = service
        self.base_url = base_url
        self.service_url = service_url
        self.settings = settings
        self._client = None

    def get_client(self, apply_settings: bool = True) -> Union[Client, tuple]:
        """
        Get or create the WSDL client for this service.

        Args:
            apply_settings: Whether to apply the configured settings

        Returns:
            Client instance or error tuple
        """
        if self._client is None:
            try:
                logger.log(
                    f"Initializing supplier client for service: {self.service}",
                    level="d",
                )
                self._client = Client(
                    wsdl=self.base_url + self.service_url,
                    settings=None if not apply_settings else self.settings,
                )
                logger.log(
                    f"Supplier client for {self.service} initialized successfully",
                    level="d",
                )
            except Exception as e:
                logger.log(
                    f"Failed to initialize supplier client for {self.service}: {e}",
                    level="c",
                )
                return (
                    {
                        "source": "Server",
                        "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                        "message": "Please contact the administrator",
                        "details": str(e),
                    },
                    HTTPStatus.INTERNAL_SERVER_ERROR,
                )

        return self._client


class SupplierExtension:
    """
    Flask extension for managing external data supplier connections.

    This extension handles the initialization and management of connections to external
    data supplier APIs, including certificate validation and fail-fast startup behavior.
    """

    def __init__(self, app: Optional[Flask] = None):
        """
        Initialize the supplier extension.

        Args:
            app: Flask application instance (optional)
        """
        self.app = app
        self.base_url = None
        self.username = None
        self.password = None
        self.service_urls = {}
        self.settings = None
        self.clients = {}

        if app is not None:
            self.init_app(app)

    def init_app(self, app: Flask) -> None:
        """
        Initialize the extension with the Flask application.

        Args:
            app: Flask application instance
        """
        self.app = app

        try:
            # Load and validate configuration
            self._load_configuration()

            # Validate certificate/connection
            self._validate_supplier_connection()

            # Initialize settings
            self._initialize_settings()

            # Store extension in app
            app.supplier = self

            logger.log("Supplier extension initialized successfully", level="i")

        except Exception as e:
            error_msg = f"Supplier extension initialization failed: {e}"
            self._terminate_flask_on_supplier_failure(error_msg)

    def _load_configuration(self) -> None:
        """
        Load and validate supplier configuration from environment variables.

        Raises:
            ValueError: If required configuration is missing
        """
        # Get required base URL
        self.base_url = os.getenv("SUPPLIER_BASE_URL")
        if not self.base_url:
            raise ValueError("Missing SUPPLIER_BASE_URL environment variable")

        # Get credentials
        self.username = os.getenv("DATA_SUPPLIER_USERNAME")
        self.password = os.getenv("DATA_SUPPLIER_PASSWORD")

        if not self.username or not self.password:
            raise ValueError(
                "Missing DATA_SUPPLIER_USERNAME or DATA_SUPPLIER_PASSWORD environment variables"
            )

        # Load service URLs
        self.service_urls = {
            "balance": os.getenv("BALANCE_ROOT"),
            "company_card": os.getenv("COMPANYCARD_ROOT"),
            "company": os.getenv("COMPANY_ROOT"),
            "financial_statement": os.getenv("FINANCIALSTATEMENT_ROOT"),
            "legal_procedures": os.getenv("LEGALPROCEDURES_ROOT"),
            "person_report": os.getenv("PERSONREPORT_ROOT"),
            "person": os.getenv("PERSON_ROOT"),
            "representative_card": os.getenv("REPRESENTATIVECARD_ROOT"),
            "shareholders": os.getenv("SHAREHOLDERS_ROOT"),
            "shares": os.getenv("SHARES_ROOT"),
            "visura": os.getenv("VISURA_ROOT"),
            "report": os.getenv("REPORT_ROOT"),
        }

        # Check for missing service URLs
        missing_services = [
            service for service, url in self.service_urls.items() if not url
        ]
        if missing_services:
            raise ValueError(
                f"Missing service URL environment variables: {', '.join(missing_services)}"
            )

    def _validate_supplier_connection(self) -> None:
        """
        Validate the supplier connection by checking certificate and connectivity.

        Raises:
            ConnectionError: If connection validation fails
        """
        try:
            logger.log("Validating supplier connection...", level="d")

            # Test connection to base URL
            response = requests.get(self.base_url, timeout=10, verify=True)

            if response.status_code >= 400:
                raise ConnectionError(
                    f"Supplier server returned HTTP {response.status_code}"
                )

            logger.log("Supplier connection validation successful", level="d")

        except requests.exceptions.SSLError as e:
            raise ConnectionError(f"SSL certificate validation failed: {e}")
        except requests.exceptions.Timeout as e:
            raise ConnectionError(f"Connection timeout to supplier: {e}")
        except requests.exceptions.ConnectionError as e:
            raise ConnectionError(f"Failed to connect to supplier: {e}")
        except Exception as e:
            raise ConnectionError(f"Supplier connection validation failed: {e}")

    def _initialize_settings(self) -> None:
        """Initialize Zeep client settings."""
        self.settings = Settings(
            strict=False, xml_huge_tree=True, xsd_ignore_sequence_order=True
        )

    def _terminate_flask_on_supplier_failure(self, error_message: str) -> None:
        """
        Terminate Flask application when supplier initialization fails.

        Args:
            error_message: The error message describing the failure
        """
        logger.log(
            f"CRITICAL: Supplier initialization failed - {error_message}", level="c"
        )
        logger.log(
            "Flask application cannot start without a functional supplier connection. Terminating...",
            level="c",
        )
        logger.log(
            "Please check your supplier configuration and certificate validity.",
            level="c",
        )

        # Force exit the application
        sys.exit(1)

    def get_client(
        self, service: str, apply_settings: bool = True
    ) -> Union[SupplierClient, tuple]:
        """
        Get a supplier client for the specified service.

        Args:
            service: The service name
            apply_settings: Whether to apply configured settings

        Returns:
            SupplierClient instance or error tuple
        """
        if service not in self.service_urls:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.BAD_REQUEST,
                    "message": f"Unknown service '{service}'",
                    "details": f"Available services: {', '.join(self.service_urls.keys())}",
                },
                HTTPStatus.BAD_REQUEST,
            )

        if service not in self.clients:
            self.clients[service] = SupplierClient(
                service=service,
                base_url=self.base_url,
                service_url=self.service_urls[service],
                settings=self.settings,
            )

        return self.clients[service]

    def get_service_client(
        self, service: str, apply_settings: bool = True
    ) -> Union[Client, tuple]:
        """
        Get a WSDL client for the specified service (backward compatibility).

        Args:
            service: The service name
            apply_settings: Whether to apply configured settings

        Returns:
            Client instance or error tuple
        """
        supplier_client = self.get_client(service, apply_settings)
        if isinstance(supplier_client, tuple):
            return supplier_client

        return supplier_client.get_client(apply_settings)

    @property
    def DATA_SUPPLIER_USERNAME(self) -> str:
        """Get the supplier username for backward compatibility."""
        return self.username

    @property
    def DATA_SUPPLIER_PASSWORD(self) -> str:
        """Get the supplier password for backward compatibility."""
        return self.password
