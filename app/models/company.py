from typing import List, Union

from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql.expression import BinaryExpression
from typing_extensions import Self

from app.extensions import db, session, uuid
from app.utils import utcnow


class Company(db.Model):
    __tablename__ = "company"
    id = db.Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid,
    )
    vat_code = db.Column(db.String(11), index=True, unique=True)
    tax_code = db.Column(db.String(16), index=True, unique=True)
    ranking = db.Column(db.Float)
    name = db.Column(db.String(255), nullable=False)
    crif_number = db.Column(db.String(255), nullable=False)
    activity_description = db.Column(db.Text, nullable=False)
    address = db.Column(db.String(255), nullable=False)
    town = db.Column(db.String(255), nullable=False)
    village = db.Column(db.String(255))
    zip_code = db.Column(db.String(255))
    province_code = db.Column(db.String(255))
    ateco_code = db.Column(db.String(255))
    ateco_description = db.Column(db.String(255))
    province_description = db.Column(db.String(255))
    description = db.Column(db.String(200))
    region = db.Column(db.String(200))
    cciaa = db.Column(db.String(255))
    rea = db.Column(db.String(255))
    legal_form_code = db.Column(db.String(255))
    legal_form_description = db.Column(db.String(255))
    unit_type_code = db.Column(db.String(255))
    unit_type_description = db.Column(db.String(255))
    company_status_code = db.Column(db.String(255))
    company_status_description = db.Column(db.String(255))
    activity_status_code = db.Column(db.String(255))
    activity_status_code_description = db.Column(db.String(255))
    flag_out_of_business = db.Column(db.Boolean())
    flag_news_available = db.Column(db.Boolean())
    flag_payment_info_available = db.Column(db.Boolean())
    last_balance_date = db.Column(db.DateTime())
    flag_belong_to_a_group = db.Column(db.Boolean())
    flag_pa = db.Column(db.String(3))
    website = db.Column(db.String(255))
    created_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow,
    )
    updated_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow,
    )

    @classmethod
    def get_by_vat_code(cls, vat_code) -> object:
        return session.query(cls).filter(cls.vat_code == vat_code).first()

    @classmethod
    def get_by_tax_code(cls, tax_code) -> object:
        return session.query(cls).filter(cls.tax_code == tax_code).first()

    @classmethod
    def get_by_id(cls, id) -> object:
        return session.query(cls).filter(cls.id == id).first()

    @classmethod
    def query_by_criteria(cls, criteria: List[BinaryExpression]) -> list:
        q = session.query(cls).filter(*criteria).all()
        return q

    @classmethod
    def as_dict(self) -> dict:
        return {
            "vat_code": self.vat_code,
            "name": self.name,
            "activity_description": self.activity_description,
            "town": self.town,
            "province_code": self.province_code,
            "rea": self.rea,
        }

    def save(self) -> None:
        session.add(self)
        session.flush()

    @classmethod
    def find(cls, company_id: UUID) -> Union[None, Self]:
        q = session.query(cls).filter(cls.id == company_id).one_or_none()
        return q
