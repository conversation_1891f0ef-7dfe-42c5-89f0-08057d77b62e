from datetime import datetime
from typing import List, Union
from uuid import UUID as UUID4

from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Query, load_only
from sqlalchemy.sql import and_
from sqlalchemy.sql.expression import BinaryExpression
from typing_extensions import Self

from app.enums.document.document import ServiceDocumentType
from app.extensions import db, session, uuid
from app.models.company import Company
from app.utils import utcnow


class Document(db.Model):
    __tablename__ = "document"
    code_atto = db.Column(db.String(255))
    company_code = db.Column(db.String(255), db.ForeignKey("company.vat_code"))
    file_url = db.Column(db.Text())
    created_at = db.Column(db.DateTime(), nullable=False, default=utcnow)
    data_chiusura_esercizio = db.Column(db.String(255))
    id = db.Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid,
    )
    num_pag_bilancio = db.Column(db.String(255))
    tax_code = db.Column(db.String(16), db.ForeignKey("person.tax_code"))
    ref_id = db.Column(db.String(255), nullable=True)
    type = db.Column(db.String(255))
    updated_at = db.Column(db.DateTime(), nullable=False, default=utcnow)
    year = db.Column(db.String(255))
    company = db.relationship(Company, backref=db.backref("document", lazy=True))

    @classmethod
    def get_by_document_id(cls, document_id) -> Union[object, None]:
        return session.query(cls).filter(cls.id == document_id).one_or_none()

    @classmethod
    def get_docs_by_ids_list(cls, document_ids) -> list:
        return (
            session.query(cls)
            .options(
                load_only(
                    cls.company_code,
                    cls.created_at,
                    cls.data_chiusura_esercizio,
                    cls.id,
                    cls.num_pag_bilancio,
                    cls.tax_code,
                    cls.ref_id,
                    cls.type,
                    cls.updated_at,
                    cls.year,
                )
            )
            .filter(cls.id.in_(document_ids))
            .all()
        )

    @classmethod
    def get_by_company_code(cls, company_code) -> list:
        return session.query(cls).filter(cls.company_code == company_code).all()

    @classmethod
    def get_by_company_code_and_document_types(
        cls, document_types: list, company_code
    ) -> list:
        return (
            session.query(cls)
            .filter(
                and_(
                    cls.type.in_(tuple(document_types)),
                    cls.company_code == company_code,
                )
            )
            .order_by(cls.year.desc() if cls.year is not None else None)
            .all()
        )

    @classmethod
    def get_company_cards_by_vat_code(cls, company_code) -> list:
        return (
            session.query(cls)
            .filter(
                cls.type.in_([ServiceDocumentType.COMPANY_CARD.value]),
                cls.company_code == company_code,
            )
            .all()
        )

    @classmethod
    def get_document_by_company_code_code_atto_year(
        cls, company_code, year, _type
    ) -> object:
        return (
            session.query(cls)
            .filter_by(company_code=company_code, year=year, type=_type)
            .first()
        )

    @classmethod
    def get_person_report(cls, tax_code) -> list:
        return (
            session.query(cls)
            .filter(
                cls.type.in_(
                    [
                        ServiceDocumentType.REPORT_STANDARD.value,
                        ServiceDocumentType.REPORT_COMPLETE.value,
                    ]
                ),
                cls.tax_code == tax_code,
            )
            .all()
        )

    @classmethod
    def get_representative_cards(cls, tax_code) -> list:
        return (
            session.query(cls)
            .filter(
                cls.type.in_(
                    [
                        ServiceDocumentType.REPRESENTATIVE_CARD_COMPLETE.value,
                        ServiceDocumentType.REPRESENTATIVE_CARD_CURRENT.value,
                        ServiceDocumentType.REPRESENTATIVE_CARD_HISTORY.value,
                    ]
                ),
                cls.tax_code == tax_code,
            )
            .all()
        )

    @classmethod
    def get_by_tax_code(cls, tax_code) -> list:
        return session.query(cls).filter_by(tax_code=tax_code).all()

    @classmethod
    def get_company_report(cls, company_code, product_types):
        return (
            session.query(cls)
            .filter(
                cls.type.in_(tuple(product_types)),
                cls.company_code == company_code,
            )
            .first()
        )

    @classmethod
    def get_by_tax_code_and_document_type(cls, tax_code, document_type) -> object:
        return (
            session.query(cls).filter_by(tax_code=tax_code, type=document_type).first()
        )

    @classmethod
    def get_visura_by_company_code(cls, company_code) -> list:
        return (
            session.query(cls)
            .filter(
                cls.company_code == company_code,
                cls.type.in_(
                    [
                        ServiceDocumentType.VISURA.value,
                        ServiceDocumentType.VISURA_HISTORIC.value,
                    ]
                ),
            )
            .all()
        )

    @classmethod
    def query_by_criteria(cls, *criteria: List[BinaryExpression]) -> list:
        """
        Fetches requests from the database based on the provided criteria.

        Args:
            *criteria (List[BinaryExpression]): A list of SQLAlchemy binary expressions
            to filter the query.

        Returns:
            list: A list of requests that match the criteria.
        """
        if not criteria:
            return session.query(cls).all()
        return session.query(cls).filter(*criteria).all()

    def as_dict(self) -> dict:
        return {
            c.name: (
                str(getattr(self, c.name))
                if isinstance(getattr(self, c.name), (datetime, UUID4))
                else getattr(self, c.name)
            )
            for c in self.__table__.columns
        }

    def save(self, autocommit: bool = False) -> None:
        session.add(self)
        session.flush()
        if autocommit:
            session.commit()

    def update(self, autocommit: bool = False) -> None:
        session.flush()
        if autocommit:
            session.commit()

    @classmethod
    def find(cls, document_id: UUID) -> Union[Self, None]:
        return session.query(cls).filter(cls.id == document_id).one_or_none()

    @classmethod
    def query(cls, filters: List[BinaryExpression]) -> Query:
        return session.query(cls).filter(*filters)
