"""
This module defines the `Request` class, which models a request within the system,
typically for documents or services.  The class provides methods for creating, updating,
deleting, and querying requests.  It also handles operations like approval,
rejection, and the checking  of existing requests to prevent duplicates.

Attributes:
    - created_at: Timestamp indicating when the request was created.
    - document_id: Foreign key to the associated document.
    - id: Unique identifier for the request, generated automatically.
    - request_timestamp: Timestamp indicating when the request was made.
    - response_timestamp: Timestamp indicating when the request was responded to.
    - result_code: Code representing the result of the request.
    - result_severity: The severity level of the request result.
    - purchase: Boolean flag indicating whether the request involves a purchase.
    - updated_at: Timestamp indicating the last time the request was updated.
    - user_id: Foreign key to the user who made the request.
    - status: Current status of the request (e.g., draft, pending, accepted).
    - coc: Center of cost associated with the request.
    - requested_at: Foreign key to the user who initiated the action.
    - bought_by: Identifier of the user who completed the purchase.
    - service_request_info: Additional information about the service request.
    - company_id: Foreign key to the associated company.
    - person_id: Foreign key to the associated person.
    - action: The action associated with the request (e.g., buy, sell).

Methods:
    - get_by_user_id: Retrieves requests made by a specific user.
    - get_by_id: Retrieves a specific request by its ID.
    - save: Saves the request object to the database.
    - update: Updates the request object with new data.
    - delete: Deletes the request object from the database.
    - ask_request: Marks the request as asked.
    - as_dict: Converts the request object to a dictionary.
    - update_values_: Updates specific fields of the request object.
    - query_by_criteria: Fetches requests based on certain criteria.
    - approve: Marks the request as approved and updates it with response data.
    - reject: Marks the request as rejected.
    - get_all: Retrieves all requests for a specific user within a year.
    - request_already_exists: Checks if a similar request already exists.
"""

from datetime import datetime
from typing import List, Union
from uuid import UUID as UUID4

from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.sql.expression import BinaryExpression, or_

from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.request.action import ActionEnum
from app.enums.request.status import StatusEnum
from app.extensions import Model, db, session, uuid
from app.models.company import Company
from app.models.document import Document
from app.models.person import Person
from app.models.user import User
from app.utils import utcnow


class Request(db.Model):
    """
    Represents a request within the system, typically for a document or service.
    """

    __tablename__ = "request"
    # pylint: disable=no-member
    created_at = db.Column(db.DateTime(), nullable=False, default=utcnow)
    document_id = db.Column(
        UUID(as_uuid=True),
        db.ForeignKey("document.id"),
        nullable=True,
    )
    id = db.Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid,
    )
    request_timestamp = db.Column(db.DateTime())
    response_timestamp = db.Column(db.DateTime())
    result_code = db.Column(db.String(55))
    result_severity = db.Column(db.String(55))
    purchase = db.Column(db.Boolean())
    updated_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow(),
    )
    user_id = db.Column(db.String(25), db.ForeignKey("user.user_id"))
    status = db.Column(
        db.Enum(
            *StatusEnum.__set_members__(),
            name="UserStatusEnum",
            validate_strings=True,
            create_type=True,
            drop_type=False,
        ),
        server_default=StatusEnum.DRAFT.value,
        nullable=False,
    )
    coc = db.Column(db.String(55), nullable=True)
    requested_at = db.Column(
        UUID(as_uuid=True),
        db.ForeignKey("user.id"),
        nullable=True,
    )
    bought_by = db.Column(db.String(25), nullable=True)
    service_request_info = db.Column(JSONB, nullable=True)
    company_id = db.Column(
        UUID(as_uuid=True), db.ForeignKey("company.id"), nullable=True
    )
    person_id = db.Column(UUID(as_uuid=True), db.ForeignKey("person.id"), nullable=True)
    action = db.Column(
        db.Enum(
            *ActionEnum.__set_members__(),
            name="RequestActionEnum",
            validate_strings=True,
            create_type=True,
            drop_type=False,
        ),
        server_default=ActionEnum.BUY.value,
        nullable=True,
    )
    # pylint: enable=no-member

    @classmethod
    def get_by_user_id(cls, user_id) -> list:
        """
        Retrieves a list of objects from the database that match the specified user ID.

        Args:
            user_id (str): The user ID to filter the records.

        Returns:
            list: A list of objects that match the provided user ID.
        """
        return session.query(cls).filter(cls.user_id == user_id).all()

    @classmethod
    def get_by_id(cls, id: int) -> Union[object, None]:
        """
        Retrieves a single object from the database based on the specified ID.

        Args:
            id (int): The unique identifier of the object to retrieve.

        Returns:
            Union[object, None]: The object that matches the provided ID, or None if not found.
        """
        return session.query(cls).filter(cls.id == id).one_or_none()

    def save(self, autocommit: bool = False) -> None:
        """
        Saves the current request object to the database.

        Args:
            autocommit (bool): Whether to commit the transaction after saving. Defaults to False.

        Returns:
            None
        """
        session.add(self)
        session.flush()
        if autocommit:
            session.commit()

    def update(self, fields: dict, autocommit: bool = False) -> None:
        """
        Updates the fields of the request object and optionally commits the transaction.

        Args:
            fields (dict): A dictionary of field names and values to update.
            autocommit (bool): Whether to commit the transaction after updating. Defaults to False.

        Returns:
            None
        """
        for attr, val in fields.items():
            setattr(self, attr, val)
        session.flush()
        if autocommit:
            session.commit()

    def delete(self, autocommit: True) -> None:
        """
        Deletes the current request object from the database and optionally commits the transaction.

        Args:
            autocommit (bool): Whether to commit the transaction after deletion. Defaults to True.

        Returns:
            None
        """
        session.delete(self)
        session.flush()
        if autocommit:
            session.commit()

    def ask_request(self) -> object:
        """
        Marks the request as asked by updating its status and other relevant fields.

        Returns:
            object: The updated request object.
        """
        self.update_values_(
            **{
                "status": StatusEnum.PENDING.value,
                "purchase": False,
                "request_timestamp": utcnow(),
            }
        )
        session.add(self)
        session.flush()
        return self

    def as_dict(self) -> dict:
        """
        Converts the request object into a dictionary format.

        Returns:
            dict: A dictionary representation of the request object.
        """
        return {
            c.name: (
                str(getattr(self, c.name))
                if isinstance(getattr(self, c.name), (datetime, UUID4))
                else getattr(self, c.name)
            )
            for c in self.__table__.columns
        }

    def update_values_(
        self, return_itself: bool = False, **fields
    ) -> Union[None, object]:
        """
        Updates the fields of the request object and optionally returns the updated object.

        Args:
            return_itself (bool): If True, returns the updated request object. Defaults to False.
            **fields: The fields and their new values to update.

        Returns:
            Union[None, object]: The updated request object if return_itself is True;
            otherwise, None.
        """
        self.__dict__.update(**fields)
        if return_itself:
            return self

    @classmethod
    def query_by_criteria(cls, *criteria: List[BinaryExpression]) -> list:
        """
        Fetches requests from the database based on the provided criteria.

        Args:
            *criteria (List[BinaryExpression]): A list of SQLAlchemy binary expressions
            to filter the query.

        Returns:
            list: A list of requests that match the criteria.
        """
        if not criteria:
            return session.query(cls).all()
        return session.query(cls).filter(*criteria).all()

    def approve(self, document_response: dict) -> dict:
        """
        Approves the request by updating it with the provided document response data
        and marking it as accepted.

        Args:
            document_response (dict): A dictionary containing the response data for the document.

        Returns:
            dict: A dictionary representation of the approved request.
        """

        def remove_keys(doc: dict) -> dict:
            pops = ("id", "created_at", "request_timestamp", "residual_credits")
            doc_ = doc.copy()
            for k in pops:
                doc_.pop(k) if k in doc_ else None
            return doc_

        newfields = remove_keys(document_response)
        document_id = (
            document_response.get("document_id")
            if document_response.get("document_id") is not None
            else document_response.get("id")
        )
        newfields.update({"document_id": document_id})
        updatable = {
            **self.as_dict(),
            **newfields,
            "status": StatusEnum.ACCEPTED.value,
            "user_id": self.user_id,
            "requested_at": self.requested_at,
            "service_request_info": self.service_request_info,
            "updated_at": utcnow(),
            "response_timestamp": (
                utcnow()
                if newfields.get("response_timestamp") is None
                else newfields["response_timestamp"]
            ),
        }
        self.update(updatable)
        return self.as_dict()

    def reject(self, document_id: int = None) -> dict:
        """
        Marks the request as rejected by updating its status and associated document ID.

        Args:
            document_id (int, optional): The ID of the document associated with the rejection.
            Defaults to None.

        Returns:
            dict: A dictionary representation of the rejected request.
        """
        self.update(
            {
                "status": StatusEnum.REJECTED.value,
                "document_id": document_id,
                "response_timestamp": utcnow(),
            }
        )
        return self.as_dict()

    @classmethod
    def get_all(cls, id: UUID, user_id: str, year_filter: dict) -> list:
        """
        Retrieves all requests for a specific user within a given year,
        filtered by user role and status.

        Args:
            id (UUID): The UUID of the user making the request.
            user_id (str): The user ID of the requester.
            year_filter (dict): A dictionary containing the year to filter requests by.

        Returns:
            list: A list of requests that match the criteria.
        """
        document_model: Document = Model(
            Document.__tablename__.capitalize()
        ).get_model()
        user_model: User = Model(User.__tablename__.capitalize()).get_model()

        approver: User = (
            session.query(user_model.ytd_approved_cost)
            .filter(user_model.id == id)
            .one()
        )
        return_value = {
            StatusEnum.DRAFT.value: list(),
            "processed": list(),
            "ytd_approved_cost": float(round(approver.ytd_approved_cost, 2)),
        }

        # Filtering by user filters
        filters_ = list()
        if year_filter:
            start_ = datetime.strptime(str(int(year_filter["year"]) - 1), "%Y")
            end_ = datetime.strptime(str(int(year_filter["year"]) + 1), "%Y")
            filters_.append(cls.created_at <= end_)
            filters_.append(cls.created_at >= start_)

        # Query draft requests
        qdraft_filters = [
            or_(cls.user_id == user_id),
            cls.status == StatusEnum.DRAFT.value,
            cls.document_id is not None,
        ]
        qdraft_filters = qdraft_filters + filters_

        draft_requests = session.query(cls).filter(*qdraft_filters).all()
        for draft in draft_requests:
            cls_entity = Company if draft.company_id is not None else Person
            _filter = "company_id" if cls_entity is Company else "person_id"
            entity = cls_entity.get_by_id(getattr(draft, _filter))
            attr = "vat_code" if cls_entity is Company else "tax_code"
            requester: User = (
                session.query(User).filter(User.user_id == draft.user_id).one_or_none()
            )
            document: Document = document_model.get_by_document_id(draft.document_id)
            name = (
                entity.name
                if cls_entity is Company
                else f"{entity.name} {entity.surname}"
            )
            _d = {
                **draft.as_dict(),
                "holder": name,
                attr: getattr(entity, attr),
                "document_type": document.type,
                "document_id": str(document.id),
                "requester_name": requester.name,
                "requester_surname": requester.surname,
                "document_price": getattr(DocsPricesEnum, document.type.upper()).value,
            }
            return_value[StatusEnum.DRAFT.value].append(_d)

        # Query by approval requests
        qfilters = [
            or_(cls.requested_at == id),
            cls.status != StatusEnum.DRAFT.value,
        ]
        qfilters = qfilters + filters_
        notdraft_requests = session.query(cls).filter(*qfilters).all()
        for notdraft in notdraft_requests:
            cls_entity = Company if notdraft.company_id is not None else Person
            _filter = "company_id" if cls_entity is Company else "person_id"
            entity = cls_entity.get_by_id(getattr(notdraft, _filter))
            attr = "vat_code" if cls_entity is Company else "tax_code"
            requester: User = (
                session.query(User)
                .filter(User.user_id == notdraft.user_id)
                .one_or_none()
            )

            document_type = notdraft.service_request_info["method_params"][
                "document_type"
            ]
            document_type_converted = ServiceDocumentType(document_type).value

            name = (
                entity.name
                if cls_entity is Company
                else f"{entity.name} {entity.surname}"
            )
            _d = {
                **notdraft.as_dict(),
                "holder": name,
                attr: getattr(entity, attr),
                "requester_name": requester.name,
                "requester_surname": requester.surname,
                "document_price": getattr(
                    DocsPricesEnum, document_type_converted.upper()
                ).value,
                "document_type": document_type_converted,
            }
            return_value["processed"].append(_d)
        return return_value

    @classmethod
    def request_already_exists(cls, new_request: dict, existing_request: dict) -> bool:
        """
        Checks if a new purchase request already exists by comparing it with an existing request.

        Args:
            new_request (dict): The new request to be checked.
            existing_request (dict): The existing request to compare against.

        Returns:
            bool: True if the request already exists, False otherwise.
        """
        return new_request == existing_request
