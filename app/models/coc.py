"""
This module defines the `Coc` model, which represents cost centers used to track expenses
within an organization. It provides functionality to:

- Retrieve all or active CoCs.
- Check existence by engagement partner email.
- Insert or update CoC entries from external sources.
- Sanitize and validate incoming CoC data.
- Fetch user info from the local database or Azure AD.
- Persist raw imported CoC data locally or to Azure Blob Storage for audit and traceability.

The model includes detailed change tracking, including date of modification and
historical differences, for auditability and compliance.
"""

import json
import os
from datetime import datetime
from pathlib import Path
from uuid import UUID as UUID4

import requests
from azure.core.exceptions import ClientAuthenticationError
from azure.identity import EnvironmentCredential
from azure.storage.blob import BlobServiceClient
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm.attributes import flag_modified

from app.config import logger
from app.enums.coc.coc import CocColumns
from app.enums.user.resources import UserResources
from app.extensions import db, session, uuid
from app.models.user import User
from app.utils import utcnow


class Coc(db.Model):
    """
    Represents a cost center in the system. A cost center is used to track where expenses are
    occurring within an organization.

    Attributes:
        changes (JSONB): Stores changes made to the cost center.
        client (str): The name of the client or entity associated with the cost center.
        closed (str): Indicates if the cost center is closed.
        code (str): A unique identifier for the cost center, serving as a code.
        deactivated_date (datetime, nullable): The date on which the cost center was deactivated.
        engagement_partner (str): The engagement partner's name associated with the cost center.
        engagement_partner_email (str): A foreign key linking to the engagement partner's email.
        engagement_partner_id (str): A foreign key linking to the user ID of the engagement partner.
        id (UUID): A unique UUID identifier for the cost center, automatically generated.
        imported_date (datetime): The date on which the cost center was first imported/created.
        is_expenses_blocked (str): Indicates if expenses are blocked.
        last_edit_date (datetime): The date on which the cost center was last edited.
        parnr (string): The partecipating partner employee id coming from external service
        participating_partner (str): Participating partner identifier.
        participating_partner_name (str): Name of the participating partner.
        profit_center (str): Profit center code.
        service_line_code (str): Service line code.
        wbs_description (str): Description of the WBS code.
    """

    __tablename__ = "coc"
    changes = db.Column(JSONB, nullable=False, default=list)
    closed = db.Column(db.Boolean, nullable=False, default=False)
    code = db.Column(db.String(255), nullable=False)
    company_customer_name = db.Column(db.String(255), nullable=False)
    deactivated_date = db.Column(db.DateTime(), nullable=True)
    description = db.Column(db.String(255), nullable=True)
    engagement_partner = db.Column(db.String(255), nullable=False)
    engagement_partner_email = db.Column(db.String(255), db.ForeignKey("user.email"))
    engagement_partner_id = db.Column(UUID(as_uuid=True), db.ForeignKey("user.id"))
    id = db.Column(UUID(as_uuid=True), primary_key=True, nullable=False, default=uuid)
    imported_date = db.Column(db.DateTime(), nullable=False, default=utcnow)
    is_expenses_blocked = db.Column(db.String(255), nullable=True)
    last_edit_date = db.Column(db.DateTime(), nullable=True)
    parnr = db.Column(db.String(255), nullable=False)
    participating_partner = db.Column(db.String(255), nullable=True)
    participating_partner_name = db.Column(db.String(255), nullable=True)
    profit_center = db.Column(db.String(255), nullable=True)
    service_line_code = db.Column(db.String(255), nullable=True)

    def as_dict(self) -> dict:
        """
        Converts the current CoC instance into a dictionary format.

        Returns:
            dict: A dictionary with column names as keys and their values as values.
                  Datetime and UUID fields are serialized appropriately.
        """
        return {
            c.name: (
                getattr(self, c.name).isoformat()
                if isinstance(getattr(self, c.name), datetime)
                else (
                    str(getattr(self, c.name))
                    if isinstance(getattr(self, c.name), UUID4)
                    else getattr(self, c.name)
                )
            )
            for c in self.__table__.columns
        }

    @classmethod
    def get_all(cls) -> list:
        """
        Retrieves all CoC records from the database.

        Returns:
            list: A list of all CoC instances.
        """
        return session.query(cls).all()

    @classmethod
    def get_all_active(cls) -> list:
        """
        Retrieves all active (non-closed) CoC records from the database.

        Returns:
            list: A list of active CoC instances.
        """
        return session.query(cls).filter(cls.closed is False).all()

    @classmethod
    def exists_by_engagement_partner_email(cls, email: str) -> bool:
        """
        Checks if a CoC with the given engagement partner email exists.

        Args:
            email (str): The engagement partner's email to check for.

        Returns:
            bool: True if a CoC with the given email exists, False otherwise.
        """
        return (
            session.query(cls)
            .filter(cls.engagement_partner_email == email, cls.closed is False)
            .count()
            > 0
        )

    @classmethod
    def update_or_insert(cls, coc_list: list) -> None:
        """
        Updates existing CoCs or inserts new CoCs based on the incoming coc_list.

        Args:
            coc_list (list): A list of dictionaries containing CoC coc_list.
        """

        try:
            cls.save_data_as_json(coc_list)
        except Exception as e:
            logger.log(f"Failed_to_save_data_file: {str(e)}", "i")

        for new_coc in cls.validate_and_process_columns(coc_list):
            existing_coc = (
                session.query(cls)
                .filter_by(code=new_coc.get(CocColumns.CODE.value))
                .one_or_none()
            )
            if existing_coc:
                cls.update_existing(existing_coc, new_coc)
            else:
                cls.create_new(
                    new_coc,
                    cls.fetch_user_from_db_or_azure(
                        new_coc.get(CocColumns.PARNR.value)
                    ),
                )

    @classmethod
    def validate_and_process_columns(cls, coc_list: list) -> list:
        """
        Validates and processes the incoming coc list.

        This includes checking that all keys in each item are valid column names
        by checking them againsta the enumerable CocColumns and sanitizing values
        such as converting 'Closed' to appropriate boolean values.

        Args:
            coc_list (list): A list of dictionaries containing CoC records.

        Raises:
            ValueError: If invalid columns are found in any item.
        """
        valid_columns = {col.value for col in CocColumns}
        print("Coc valid columns:", valid_columns)

        for coc in coc_list:
            invalid_keys = [key for key in coc.keys() if key not in valid_columns]
            if invalid_keys:
                raise ValueError(f"Invalid columns found in data: {invalid_keys}")

            # Sanitize and process specific fields
            if CocColumns.CLOSED.value in coc:
                coc[CocColumns.CLOSED.value] = (
                    coc[CocColumns.CLOSED.value].lower() == "closed"
                )

        return coc_list

    @classmethod
    def get_user_info_from_azure(cls, employee_id) -> dict:
        """
        Fetches user information from Azure AD based on the employee ID.

        Args:
            employee_id (str): The employee ID to search for.

        Returns:
            dict: A dictionary containing the user's information.
        """
        try:
            credential = EnvironmentCredential()
            token = credential.get_token(os.getenv("AZURE_SCOPE"))
            headers = {
                "Authorization": f"Bearer {token.token}",
                "Content-Type": "application/json",
            }
            graph_url = os.getenv("GRAPH_URL")
            filter_query = f"{graph_url}/users?$filter=employeeId eq '{employee_id}'"
            response_raw = requests.get(filter_query, headers=headers, timeout=1000)
            response = response_raw.json()

            user_info = response.get("value", [])
            if user_info:
                user_info = user_info[0]
                return {
                    "id": user_info.get("id"),
                    "email": user_info.get("mail"),
                    "name": user_info.get("givenName"),
                    "surname": user_info.get("surname"),
                }
            raise ValueError("User with the given EmployeeID not found in Azure AD.")

        except ClientAuthenticationError as e:
            raise ValueError(f"Authentication failed: {str(e)}") from e
        except Exception as e:
            raise ValueError(
                f"An error occurred while fetching user info: {str(e)}"
            ) from e

    @classmethod
    def create_new(cls, coc, approver) -> "Coc":
        """
        Creates a new CoC record based on the provided coc.

        Args:
            coc (dict): A dictionary containing new CoC data.

        Returns:
            Coc: The newly created CoC instance.

        Raises:
            ValueError: If PARNR is empty or not provided.
        """
        try:
            session.begin_nested()
            new_coc = cls(
                closed=(
                    bool(coc.get(CocColumns.CLOSED.value))
                    if CocColumns.CLOSED.value in coc
                    else False
                ),
                code=coc.get(CocColumns.CODE.value),
                company_customer_name=coc.get(CocColumns.COMPANY_CUSTOMER_NAME.value),
                description=coc.get(CocColumns.DESCRIPTION.value),
                engagement_partner=coc.get(CocColumns.ENGAGEMENT_PARTNER.value),
                engagement_partner_id=approver.id,
                engagement_partner_email=approver.email,
                is_expenses_blocked=coc.get(CocColumns.IS_EXPENSES_BLOCKED.value)
                or None,
                parnr=coc.get(CocColumns.PARNR.value),
                participating_partner=coc.get(CocColumns.PARTICIPATING_PARTNER.value),
                participating_partner_name=coc.get(
                    CocColumns.PARTICIPATING_PARTNER_NAME.value
                ),
                profit_center=coc.get(CocColumns.PROFIT_CENTER.value),
                service_line_code=coc.get(CocColumns.SERVICE_LINE_CODE.value) or None,
            )

            session.add(new_coc)
            session.commit()

        except Exception as e:
            session.rollback()
            raise e

        return new_coc

    @classmethod
    def update_existing(cls, coc, new_coc):
        """
        Updates an existing CoC record with new data and tracks the changes.

        Args:
            coc (Coc): The existing CoC instance to be updated.
            new_coc (dict): A dictionary containing the new data for the CoC.

        Updates:
            - Attributes of the CoC instance that have changed.
            - The 'closed' status and 'deactivated_date' if the CoC is marked as closed.
            - The 'changes' JSONB field to track the history of changes made.
            - The 'last_edit_date' field to the current datetime.
        """
        changes = []
        for key, value in new_coc.items():
            attribute = CocColumns(key).name.lower()
            if hasattr(coc, attribute):
                old_value = getattr(coc, attribute)
                if old_value != value:
                    change = {
                        "attribute": attribute,
                        "old_value": old_value,
                        "new_value": value,
                        "changed_at": utcnow(),
                    }
                    changes.append(change)
                    setattr(coc, attribute, value)

                    if attribute == "closed":
                        coc.deactivated_date = (
                            utcnow()
                            if value and not old_value
                            else (
                                None
                                if not value and old_value
                                else coc.deactivated_date
                            )
                        )

        if changes:
            coc.changes.extend(changes)
            coc.last_edit_date = utcnow()

            # SQLAlchemy needs a help to understand if an immutable has been modified
            flag_modified(coc, "changes")

        session.add(coc)
        session.commit()

    @classmethod
    def fetch_user_from_db_or_azure(cls, employee_id: str) -> User:
        """
        Fetches a user from the database or Azure AD based on the employee ID.

        Args:
            employee_id (str): The employee ID to search for.

        Returns:
            User: The fetched or newly created User instance.
        """
        # The data incoming has the Azure EmployeeID field value saved as  "0600" + employee_id or
        # "5555" + exmployee_id ofr test accounts (t-spartaxadmin, t-spartapprover, t-spartaxuser)
        if employee_id.startswith("06"):
            employee_id = employee_id[2:].lstrip("0")
            existing_user = (
                session.query(User).filter_by(employee_id=employee_id).one_or_none()
            )
        elif employee_id == "*********":
            existing_user = (
                session.query(User)
                .filter_by(employee_id=employee_id, id=os.getenv("DB_APPROVER_ID"))
                .one_or_none()
            )

        if not existing_user:
            azure_user_info = cls.get_user_info_from_azure(employee_id)
            role = "approver"
            existing_user = User(
                azure_id=azure_user_info["id"],
                email=azure_user_info["email"],
                employee_id=employee_id,
                id=uuid(),
                name=azure_user_info["name"],
                residual_credits=UserResources[role.upper()].value["residual_credits"],
                residual_requests=UserResources[role.upper()].value[
                    "residual_requests"
                ],
                role=role,
                surname=azure_user_info["surname"],
                user_id=azure_user_info["email"].split("@", 1)[0],
            )
            session.add(existing_user)
            session.commit()

        return existing_user

    @classmethod
    def save_data_as_json(cls, data: list) -> bool:
        """
        Saves the provided data as a JSON file either in Azure Storage or locally,
        based on the environment configuration.

        Args:
            data (list): The data to be saved as a JSON file.
        """

        container = os.getenv("AZURE_STORAGE_IMPORT_CONTAINER_NAME")
        folder = (
            f"{os.getenv('AZURE_STORAGE_IMPORT_FOLDER_NAME')}/"
            f"{utcnow().strftime('%d-%m-%Y_%H-%M-%S')}"
        )
        try:
            if os.getenv("ENVIRONMENT") == "development":
                logger.log("Saving_coc_data_as_json_to_local_folder", "i")
                # Use Path to create directories and file path
                local_path = Path(
                    f"{os.path.expanduser('~')}/spartax/api/temporary/{container}/{folder}"
                )

                local_path.mkdir(parents=True, exist_ok=True)
                full_path = local_path / "original.json"

                # Save data to the local file
                with open(full_path, "w", encoding="utf-8") as file:
                    json.dump(data, file, indent=4)
                return True

            logger.log("SAVIND_COC_DATA_AS_JSON_TO_STORAGE\n", "i")
            connection_string = os.getenv("AZURE_STORAGE_CONN_STRING")
            blob_service_client = BlobServiceClient.from_connection_string(
                connection_string
            )
            container_client = blob_service_client.get_container_client(container)
            blob_client = container_client.get_blob_client(f"{folder}/original.json")
            json_data = json.dumps(data)
            blob_client.upload_blob(json_data, overwrite=True)
            return True

        except Exception as e:
            logger.log(f"Saving_coc_data_as_json_failed with error: {str(e)}", "i")
            return False
