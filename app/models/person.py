from typing import List, Union

from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql.expression import BinaryExpression
from typing_extensions import Self

from app.extensions import db, session, uuid
from app.utils import utcnow


class Person(db.Model):
    __tablename__ = "person"
    id = db.Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid,
    )
    tax_code = db.Column(db.String(16), index=True, unique=True)
    ranking = db.<PERSON>umn(db.Float)
    surname = db.<PERSON>umn(db.String(55))
    name = db.Column(db.String(55))
    birth_date = db.Column(db.DateTime())
    birth_town = db.Column(db.String(55))
    birth_province = db.Column(db.String(55))
    birth_province_description = db.Column(db.String(55))
    gender = db.Column(db.String(55))
    address = db.Column(db.String(55))
    town = db.Column(db.String(55))
    village = db.Column(db.String(55))
    province = db.Column(db.String(55))
    zip = db.Column(db.String(55))
    country = db.Column(db.String(55))
    has_roles = db.Column(db.String(55))
    is_sole_trader = db.Column(db.String(55))
    is_shareholder = db.Column(db.String(55))
    created_at = db.Column(db.DateTime(), nullable=False, default=utcnow)
    updated_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow,
    )

    @classmethod
    def get_by_tax_code(cls, tax_code: str) -> object:
        return session.query(cls).filter(cls.tax_code == tax_code).first()

    @classmethod
    def get_by_id(cls, person_id: int) -> Union[object, None]:
        return session.query(cls).filter(cls.id == person_id).one_or_none()

    @classmethod
    def as_discT(self) -> dict:
        return {"tax_code": self.tax_code, "surname": self.surname, "name": self.name}

    def save(self) -> None:
        session.add(self)
        session.flush()

    @classmethod
    def find(cls, company_id: UUID) -> Union[None, Self]:
        q = session.query(cls).filter(cls.id == company_id).one_or_none()
        return q

    @classmethod
    def query_by_criteria(cls, criteria: List[BinaryExpression]) -> list:
        q = session.query(cls).filter(*criteria).all()
        return q
