"""
This module defines the PushLog model for tracking data push operations in the system.

The PushLog model maintains a record of when data is pushed to the system, including
metadata such as the source, number of records received, duration, and other contextual
information. This helps in monitoring and auditing data ingestion processes.
"""

from datetime import datetime, timezone
from uuid import UUID as UUID4

from sqlalchemy.dialects.postgresql import JSONB, UUID

from app.extensions import db, session, uuid


class PushLog(db.Model):
    """
    Represents a log entry for data pushes received by the system.
    Each entry tracks metadata about the push, including when it occurred,
    its duration, and contextual details.

    Attributes:
        id (UUID): A unique identifier for the push log entry, automatically generated.
        pushed_at (datetime): The date and time when the push was received.
        source (str): The origin of the data push (e.g., "Azure AD", "External API").
        received_records (int): The total number of records received during this push.
        duration (float): The duration of the push in seconds.
        metadata (JSONB): Additional metadata about the push
        created_at (datetime): The date and time when the push log entry was created.
    """

    __tablename__ = "push_log"
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid, nullable=False)
    completed_at = db.Column(db.DateTime, nullable=True)
    pushed_at = db.Column(
        db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False
    )
    received_records = db.Column(db.Integer, nullable=False, default=0)
    created_at = db.Column(
        db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False
    )

    def as_dict(self) -> dict:
        """
        Converts the PushLog instance to a dictionary representation.

        Returns:
            dict: A dictionary containing all model attributes with datetime and UUID values
                converted to strings.
        """
        return {
            c.name: (
                getattr(self, c.name).isoformat()
                if isinstance(getattr(self, c.name), datetime)
                else (
                    str(getattr(self, c.name))
                    if isinstance(getattr(self, c.name), UUID4)
                    else getattr(self, c.name)
                )
            )
            for c in self.__table__.columns
        }

    @classmethod
    def get_all(cls) -> list:
        """
        Retrieves all push log entries from the database.

        Returns:
            list: A list of all PushLog instances.
        """
        return session.query(cls).all()

    @classmethod
    def get_last_push_date(cls) -> datetime:
        """
        Retrieves the datetime of the most recent push log entry.

        Returns:
            datetime: The pushed_at datetime of the most recent push log entry.
            If no entries exist, returns None.
        """
        last_push = session.query(cls.pushed_at).order_by(cls.pushed_at.desc()).first()
        return last_push[0] if last_push else None

    @classmethod
    def create_push(cls) -> dict:
        """
        Creates a new push log entry with current timestamp.

        Returns:
            dict: The newly created push log entry as a dictionary.
        """
        push_log = cls()
        session.add(push_log)
        session.commit()
        return push_log.as_dict()

    @classmethod
    def complete_push(cls, push_id: UUID4, received_records: int) -> dict:
        """
        Updates a push log entry with completion details.

        Args:
            push_id: UUID of the push log entry to update
            received_records: Number of records processed in this push

        Returns:
            dict: The updated push log entry as a dictionary
        """
        push_log = session.query(cls).get(push_id)
        push_log.completed_at = datetime.now(timezone.utc)
        push_log.received_records = received_records
        session.commit()
        return push_log.as_dict()
