from datetime import datetime
from decimal import R<PERSON>UND_DOWN, <PERSON><PERSON><PERSON>
from typing import Union
from uuid import UUID as UUID4

from sqlalchemy.dialects.postgresql import UUID

from app.extensions import db, session, uuid
from app.utils import utcnow


class User(db.Model):
    """
    Represents a user in the system, managing user-specific data and interactions.

    Attributes:
        id (UUID): A unique identifier for the user, automatically generated.
        azure_id (UUID, optional): An optional Azure-specific identifier for the user.
        employee_id (str): The EmployeeId for the user in the Azure tenant
        email (str): The user's email address, unique across the system.
        name (str): The user's first name.
        role (str): The user's role in the system, determining access levels.
        surname (str): The user's surname.
        created_at (datetime): Timestamp of when the user account was created.
        updated_at (datetime): Timestamp of the last update made to the user's account.
        last_access (datetime): Timestamp of the user's last access to the system.
        residual_credits (Numeric): The remaining credits for the user.
        residual_requests (Numeric): The remaining request quota for the user.
        user_id (str): A unique, system-assigned user identifier.
        ytd_approved_cost (float): The year-to-date approved cost for the user.

    Methods:
        update(fields: dict, autocommit: bool): Updates the user's attributes based on the given fields.
        as_dict() -> dict: Returns a dictionary representation of the user's data.
        reduce_resources(amount: Union[int, float], value_type: type, resource: str, autocommit: bool):
            Reduces specified resources (credits, requests, downloads) for the user.

    The User model is integral for managing user data within the system, providing
    functionalities for updating user details, managing resources, and representing
    user data in a structured format. It interacts with the underlying database
    using SQLAlchemy, particularly targeting a PostgreSQL setup.
    """

    __tablename__ = "user"
    id = db.Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid,
    )
    azure_id = db.Column(
        UUID(as_uuid=True),
        nullable=True,
    )
    # TODO: reset back to unique=True as soon as pen test concluded
    employee_id = db.Column(db.String(10), nullable=True, unique=False)
    email = db.Column(db.String(55), nullable=True, unique=True)
    name = db.Column(db.String(128), nullable=True)
    role = db.Column(db.String(9), nullable=False)
    surname = db.Column(db.String(128), nullable=True)
    created_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow,
    )
    updated_at = db.Column(
        db.DateTime(),
        nullable=False,
        default=utcnow,
    )
    last_access = db.Column(
        db.DateTime(),
        default=utcnow,
    )
    residual_credits = db.Column(db.Numeric(10, 2), nullable=True)
    residual_requests = db.Column(db.Numeric(10, 2), nullable=True)
    user_id = db.Column(db.String(25), unique=True, autoincrement=False, nullable=False)
    ytd_approved_cost = db.Column(db.Float, nullable=True, default=0.0)

    def update(self, fields: dict, autocommit: bool = False) -> None:
        """
        Updates the attributes of a User instance with the given field values.

        This method iterates over the provided dictionary of fields and updates the corresponding
        attributes of the User instance with the new values. After updating the attributes, it
        flushes the changes to the database session. If the 'autocommit' flag is set to True,
        the method will also commit the transaction, persisting the changes to the database.

        Note: This is a class method and should be called on the User class itself, passing
        the instance to be updated as part of the fields dictionary.

        Args:
            fields (dict): A dictionary where keys are attribute names of the User model and
                           values are the new values for those attributes.
            autocommit (bool, optional): If set to True, commits the transaction after updating the
                                         attributes. Defaults to False, in which case the transaction
                                         must be committed manually elsewhere.

        Returns:
            None

        Example:
            User.update({'email': '<EMAIL>', 'name': 'Jane Doe'}, autocommit=True)
        """
        fields["updated_at"] = utcnow()

        # Update diretto nel database
        session.query(User).filter(User.id == self.id).update(
            fields, synchronize_session="fetch"
        )

        for attr, val in fields.items():
            setattr(self, attr, val)

        if autocommit:
            session.commit()

    def as_dict(self) -> dict:
        """Converts the User instance into a dictionary, suitable for JSON serialization.

        This method transforms the User object into a dictionary where keys are the names
        of the model's attributes and values are their corresponding values. Special handling
        is done for 'residual_credits' and 'residual_requests' to ensure they are rounded
        appropriately and converted to floats. Additionally, datetime and UUID fields are
        converted to strings for compatibility with JSON.

        Returns:
            dict: A dictionary representation of the User instance. Keys are the names of
                the attributes, and values are their corresponding values, with special
                handling for datetime, UUID, and numeric fields.

        Example:
            user = User(...)
            user_dict = user.as_dict()
            # user_dict can now be easily serialized to JSON

        This method is particularly useful for converting User objects to a format that can
        be easily serialized and sent over HTTP in a JSON payload, facilitating API responses.
        """
        self.residual_credits = (
            float(round(self.residual_credits, 2))
            if self.residual_credits is not None
            else round(self.residual_credits, 2)
        )
        self.residual_requests = (
            float(self.residual_requests)
            if self.residual_requests is not None
            else self.residual_requests
        )
        return {
            c.name: (
                str(getattr(self, c.name))
                if isinstance(getattr(self, c.name), (datetime, UUID4))
                else getattr(self, c.name)
            )
            for c in self.__table__.columns
        }

    def restore_resources(
        self,
        amount: Union[int, float],
        resource: str,
        autocommit: bool = True,
    ) -> Union[int, float]:
        """Restore a specific resource of a User instance by a given amount.

        This class method deducts a specified amount from a resource attribute of the User instance.
        The resource to be updated and the type of value to be deducted (int or float) are
        specified by the caller. It then updates the User instance with the new resource value.
        If the 'autocommit' flag is set to True, the transaction is committed immediately,
        persisting the change to the database.

        Note: The method assumes that the resource attribute exists in the User model and
        its value can be converted to the specified 'value_type'.

        Args:
            amount (Union[int, float]): The amount by which the resource should be restored to.
            resource (str): The name of the resource attribute to be reduced.
            autocommit (bool, optional): If True, commits the transaction after reducing the
                                        resource. Defaults to True.

        Returns:
            Union[int, float]: The new value of the resource after reduction.

        Example:
            User.restore_resources(10, 'residual_requests', autocommit=True)
        """
        if resource == "residual_requests":
            available_resources = float(getattr(self, resource))
            new_amount = available_resources + amount
            self.update({resource: new_amount})
            setattr(self, resource, new_amount)
            val = float(getattr(self, resource))
            return val

    def reduce_resources(
        self,
        amount: Union[int, float],
        resource: str,
        autocommit: bool = True,
    ) -> Union[int, float]:
        """Reduces a specific resource of a User instance by a given amount.

        This class method deducts a specified amount from a resource attribute of the User instance.
        The resource to be updated and the type of value to be deducted (int or float) are
        specified by the caller. It then updates the User instance with the new resource value.
        If the 'autocommit' flag is set to True, the transaction is committed immediately,
        persisting the change to the database.

        Note: The method assumes that the resource attribute exists in the User model and
        its value can be converted to the specified 'value_type'.

        Args:
            amount (Union[int, float]): The amount by which the resource should be reduced.
            resource (str): The name of the resource attribute to be reduced.
            autocommit (bool, optional): If True, commits the transaction after reducing the
                                        resource. Defaults to True.

        Returns:
            Union[int, float]: The new value of the resource after reduction.

        Example:
            User.reduce_resources(10, 'residual_credits', autocommit=True)
        """
        if resource == "residual_requests":
            available_resources = float(getattr(self, resource))
            new_amount = available_resources - amount
        elif resource == "residual_credits":
            amount = Decimal(str(amount))
            available_resources = Decimal(getattr(self, resource))
            update_amount = available_resources - amount
            update_amount = update_amount.quantize(Decimal("0.01"), rounding=ROUND_DOWN)
            new_amount = float(update_amount)
        self.update({resource: new_amount})
        setattr(self, resource, new_amount)
        val = float(getattr(self, resource))
        return val

    @classmethod
    def get_by_id(cls, id: UUID) -> "User":
        """
        Searches for a user based on the given criteria (user_id, email, or id).
        If the user does not exist, it creates a new one with a unique ID, the provided email, and a default role.

        Args:
            criteria (dict): A dictionary containing the search criteria. Keys should be 'user_id', 'email', or 'id'.
            role (str, optional): The role of the user, defaults to "consumer" if not provided.

        Returns:
            User: The found or newly created User instance.

        Example:
            user = User.get_or_create({'email': '<EMAIL>'})
        """
        user = cls.query.filter_by(id=id).one_or_none()
        return user

    @classmethod
    def get_by_user_id(cls, user_id: str) -> "User":
        """
        Searches for a user based on the user_id.

        Args:
            user_id (str): A string containing the user_id

        Returns:
            User: The found User instance.
        """
        user = cls.query.filter_by(user_id=user_id).one_or_none()
        return user
