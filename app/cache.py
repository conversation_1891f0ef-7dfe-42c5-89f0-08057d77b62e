"""
This module provides the implementation of the `Cache` class, which manages the connection and
interactions with a cache server.

The `Cache` class is responsible for initializing a connection to the cache server using
environment variables for configuration. It includes methods to check the connection, store,
and retrieve key-value pairs in the cache, and log interactions with the cache server.

Key functionalities provided by this module:
- Establishing a secure connection to a cache server.
- Checking the connection status.
- Logging detailed information about the cache operations.

The `Cache` instance is initialized globally within the module and can be used throughout the
application to manage caching needs.

Attributes:
    cache (Cache): A global instance of the `Cache` class used for interacting with the cache.

Classes:
    Cache: A class that encapsulates the logic for connecting to and interacting with the cache.
"""

import json
import os
import socket
import sys
from typing import Any, Optional

import redis
from redis import ConnectionPool, SSLConnection
from redis.exceptions import AuthenticationError as RedisAuthenticationError
from redis.exceptions import ConnectionError as RedisConnectionError
from redis.exceptions import TimeoutError as RedisTimeoutError

from app.config import logger


class Cache:
    """
    A class that handles interactions with a cache server for storing and retrieving
    authenticated user information.

    Attributes:
        client (redis.StrictRedis): The cache client instance used to interact
        with the cache server.
    """

    def __init__(self) -> None:
        """
        Initializes the Cache instance and establishes a connection to the cache server.
        Reads the connection settings from environment variables and configures the cache client.

        Environment Variables:
            CACHE_HOST (str): The hostname or IP address of the cache server.
            CACHE_PORT (str): The port number on which the cache server is listening.
            CACHE_PASSWORD (str): The password or access key to authenticate with the cache server.
            CACHE_SOCKET_TIMEOUT (str): The timeout value for the single socket
            CACHE_SOCKET_CONNECT_TIMEOUT (str): The timeout value for the single socket connection

        Raises:
            ConnectionError: If the connection to the cache server fails.
        """
        try:
            host = os.getenv("CACHE_HOST")
            port = int(os.getenv("CACHE_PORT"))
            password = os.getenv("CACHE_PASSWORD")
            cache_timeout = int(os.getenv("CACHE_DEFAULT_TIMEOUT", "3600"))
            logger.log(
                "Initializing connection to the cache connection pool...", level="i"
            )
            self.pool: ConnectionPool = ConnectionPool(
                connection_class=SSLConnection,
                decode_responses=True,
                host=host,
                max_connections=int(os.getenv("CACHE_POOL_MAX_CONNECTIONS")),
                password=password,
                port=port,
                socket_connect_timeout=int(os.getenv("CACHE_SOCKET_CONNECT_TIMEOUT")),
                socket_keepalive=True,
                socket_timeout=int(os.getenv("CACHE_SOCKET_TIMEOUT")),
                ssl_cert_reqs=None,
            )

            logger.log(
                "Initializing connection of the single client socket...", level="i"
            )
            self.client: redis.StrictRedis = redis.StrictRedis(
                connection_pool=self.pool
            )
            self.default_ttl = cache_timeout

            self._check_connection()
            logger.log("Connected to the cache server successfully.", level="i")

        except RedisAuthenticationError as e:
            logger.log(f"Authentication failed: {e}", level="c")
            raise ConnectionError(
                f"Failed to authenticate with the cache server: {e}"
            ) from e
        except RedisTimeoutError as e:
            logger.log(f"Connection timed out: {e}", level="c")
            raise ConnectionError(
                f"Connection to the cache server timed out: {e}"
            ) from e
        except ConnectionError as e:
            logger.log(f"Connection timed out: {e}", level="c")
            raise ConnectionError(f"Failed to connect to cache: {e}") from e

    def _check_connection(self) -> None:
        """Checks the connection to the cache server by testing network connectivity and sending a \
            PING command."""
        try:
            logger.log("Pinging the cache server...", level="i")
            if not self.client.ping():
                raise ConnectionError("Cache test ping failed")
            logger.log("Ping to the cache server successful.", level="i")

        except (socket.timeout, socket.error) as e:
            logger.log(f"Network connectivity check failed: {e}", level="c")
            raise ConnectionError(f"Network connectivity check failed: {e}") from e
        except RedisAuthenticationError as e:
            logger.log(f"Authentication failed during ping: {e}", level="c")
            raise ConnectionError(f"Cache authentication failed: {e}") from e
        except RedisTimeoutError as e:
            logger.log(f"Ping timed out: {e}", level="c")
            raise ConnectionError(f"Cache ping timed out: {e}") from e
        except RedisConnectionError as e:
            logger.log(f"Ping failed: {e}", level="c")
            raise ConnectionError(f"Cache connection failed: {e}") from e

    def set(self, key: str, value: Any, ttl: Optional[int] = 3600) -> None:
        """
        Stores a key-value pair in the cache with an optional Time-To-Live (TTL).

        Args:
            key (str): The key under which the value should be stored. Must be a string.
            value (Any): The value to store, which will be serialized to JSON if needed.
            ttl (Optional[int]): The time-to-live (TTL) for the cache entry in seconds. Defaults \
                to 3600 (1 hour).

        Raises:
            TypeError: If the key is not a string.
            redis.RedisError: If there is an error while interacting with the cache server.
        """
        if not isinstance(key, str):
            raise TypeError("Key must be a string")

        try:
            logger.log(
                f"Setting value for key: {key} with TTL of {ttl} seconds", level="i"
            )
            self.client.set(key, value, ex=ttl)
            logger.log(f"Value set for key: {key} successfully.", level="i")

        except RedisAuthenticationError as e:
            logger.log(f"Authentication failed during 'set': {e}", level="c")
            raise ConnectionError(
                f"Cache authentication failed during 'set': {e}"
            ) from e
        except RedisTimeoutError as e:
            logger.log(f"Set operation timed out for key: {key}", level="c")
            raise ConnectionError(f"Cache 'set' operation timed out: {e}") from e
        except RedisConnectionError as e:
            logger.log(f"Set operation failed for key: {key}: {e}", level="c")
            raise ConnectionError(f"Cache 'set' operation failed: {e}") from e

    def get(self, key: str) -> Optional[Any]:
        """
        Retrieves a value from the cache for the given key.

        Args:
            key (str): The key whose associated value is to be retrieved.

        Returns:
            any: The value associated with the key, deserialized from JSON if applicable.
                Returns None if the key does not exist.

        Raises:
            TypeError: If the key is not a string.
            redis.RedisError: If there is an error while interacting with the cache server.
        """
        if not isinstance(key, str):
            raise TypeError("Key must be a string")

        try:
            logger.log(f"Retrieving value for key: {key}", level="i")
            data = self.client.get(key)
            if data:
                logger.log(f"Value retrieved for key: {key}", level="i")
            else:
                logger.log(f"No value found for key: {key}", level="w")
            return json.loads(data) if data else None
        except RedisAuthenticationError as e:
            logger.log(f"Authentication failed during 'get': {e}", level="c")
            raise ConnectionError(
                f"Cache authentication failed during 'get': {e}"
            ) from e
        except RedisTimeoutError as e:
            logger.log(f"Get operation timed out for key: {key}", level="c")
            raise ConnectionError(f"Cache 'get' operation timed out: {e}") from e
        except RedisConnectionError as e:
            logger.log(f"Get operation failed for key: {key}: {e}", level="c")
            raise ConnectionError(f"Cache 'get' operation failed: {e}") from e


cache = Cache()
