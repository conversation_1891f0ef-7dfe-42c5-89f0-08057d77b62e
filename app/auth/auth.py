"""
Module for handling authentication and authorization in the application.

Provides classes and utilities for:
- JWT token validation and processing
- User authentication via Azure AD
- Session management with cookies
- Role-based authorization
"""

import base64
import hashlib
import json
import os
import time
import traceback
from datetime import datetime
from decimal import Decimal
from http import HTTPStatus
from typing import Any, Dict, Tuple

import jwt
import requests
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicNumbers
from flask import Request, current_app
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm.session import Session
from typing_extensions import Self

from app.auth.auth_config import authconfig
from app.config import logger
from app.enums.user.resources import UserResources
from app.extensions import session
from app.models.coc import Coc
from app.models.user import User
from app.utils import utcnow


class InvalidAuthorizationToken(Exception):
    """
    Invalid authorization token format.
    """

    def __init__(self, details):
        super().__init__("Invalid authorization token: " + details)


class InvalidAuthenticationToken(Exception):
    """
    Invalid authentication token format and/or token expired.
    """

    def __init__(self, details):
        super().__init__("Invalid authentication token: " + details)


class Auth:
    """
    Auth class handles all the authentication/authorization steps.

    Authentication:
        - step that is made for each endpoint that is going to be called
        (needed to verify that the user is still logged and authenticated).
        The IdToken is given upon Azure authentication and passed as header as follow:

            {Id: 'secret'}

    Authorization:
        - step executed only when GET 'user' endpoint is consumed: in this case,
        if the user is logged for the first time is going to be created, updated
        with new info if given otherwise.
        The AccessToken is given upon Azure authentication and passed as header as follow:

            {Authorization: Bearer 'secret'}

    Raises:
        :InvalidAuthenticationToken. IdToken is not valid or expired.
        :InvalidAuthorizationToken. AccessToken is not valid.
    """

    __issuer: str = authconfig.ISSUER
    __jwks: Dict = authconfig.jwks
    __updatable: Tuple = ("email", "name", "surname", "azure_id")
    __valid_audiences: Tuple = authconfig.VALID_AUDIENCES
    _decoded: Dict = dict()
    _now: datetime = utcnow()
    _session: Session = session
    __url_graph: str = authconfig.GRAPH_URL
    auth_user: Dict = dict()
    id_token: str
    is_active: bool = False
    session_cookie: str

    def __new__(cls, **kwargs) -> Self:
        obj = super().__new__(cls)
        obj.is_active = cls.is_active
        obj.auth_user = cls.auth_user
        for k, v in kwargs.items():
            setattr(obj, k, v)
        return obj

    def __init__(self, request: Request, **kwargs):

        for k, v in kwargs.items():
            setattr(self, k, v)

        self.cache = current_app.cache.client
        self.session_cookie = request.cookies.get("session_cookie")
        self.id_token = request.headers.get("Id")

        if self.session_cookie:
            self.session_cookie_authentication()
        elif self.id_token:
            self.id_token_authentication()

        self.refresh_user_resources(UserResources[self.auth_user.role.upper()])

    def session_cookie_authentication(self):
        """
        Handle Session cookie authentication with ID token as a fallback.
        If cookie is presente as cache entry key, we get the cached user data
        else, it fallbacks to the ID token authentication.
        """
        try:
            cached_user = self.cache.get(self.session_cookie)
            if cached_user is not None:
                self.auth_user = User(**json.loads(cached_user))
                return

            if self.id_token:
                return self.id_token_authentication()

        except Exception as e:
            raise InvalidAuthorizationToken(
                "Unable to get cached user entry from cache or throught ID token fallback procedure"
            ) from e

    def id_token_authentication(self):
        """
        Handle the authentication through the ID Token.
        Get or create the user and generate a new session cookie
        """
        try:
            if not self.id_token:
                raise InvalidAuthorizationToken("No id_token found")

            logger.log("Starting id token jwt validation:\n", "i")
            self._decoded = self.validate_jwt(self.id_token)
            if self._decoded:
                self._decoded["user_email"] = self._decoded["preferred_username"]
                self.is_active = True
                self.auth_user: User = (
                    self._session.query(User)
                    .filter(User.email == self._decoded["user_email"])
                    .one_or_none()
                )
            if self.id_token is not None and self.auth_user is None:
                logger.log("Starting access jwt validation data:\n", "i")
                user_info = self.prepare_db_user(self._decoded)
                self.auth_user = User(**user_info)
                try:
                    logger.log("Adding new user for the first time:\n", "i")
                    self._session.add(self.auth_user)
                    self._session.flush()
                    self._session.commit()
                except IntegrityError as e:
                    self._session.rollback()
                    return (
                        {
                            "source": "API root auth",
                            "error": HTTPStatus.CONFLICT,
                            "message": e,
                        },
                        HTTPStatus.CONFLICT,
                    )
                except SQLAlchemyError as e:
                    self._session.rollback()
                    return (
                        {
                            "source": "API root auth",
                            "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                            "message": "A database error occurred: " + str(e),
                        },
                        HTTPStatus.INTERNAL_SERVER_ERROR,
                    )
            if not self.auth_user:
                raise InvalidAuthorizationToken("Failed user retrival")

            exp_time = self._decoded.get("exp")
            current_time = int(time.time())
            self.ttl = max(exp_time - current_time - 10, 0)

            if not self.session_cookie:
                self.session_cookie = hashlib.sha256(os.urandom(64)).hexdigest()

        except Exception as e:
            raise InvalidAuthorizationToken(e.args[0]) from e

    def ensure_bytes(self, key):
        """
        Ensures that the given key is in bytes format.

        This method checks if the provided key is a string, and if so, encodes it
        into bytes using UTF-8 encoding.

        Args:
            key (str or bytes): The key to ensure is in bytes format.

        Returns:
            bytes: The key in bytes format.
        """
        logger.log("Attempting ensuring bytes:\n", "i")
        if isinstance(key, str):
            logger.log("ENCODING KEY...\n", "i")
            key = key.encode("utf-8")
        return key

    def decode_value(self, val):
        """
        Decodes a base64-encoded string to an integer.

        This method takes a base64 URL-safe encoded string, decodes it, and converts
        the resulting bytes into an integer.

        Args:
            val (str): The base64-encoded string to decode.

        Returns:
            int: The decoded integer value.
        """
        decoded = base64.urlsafe_b64decode(self.ensure_bytes(val) + b"==")
        return int.from_bytes(decoded, "big")

    def rsa_pem_from_jwk(self, jwk):
        """
        Converts a JSON Web Key (JWK) to an RSA public key in PEM format.

        This method constructs an RSA public key using the modulus (n) and exponent (e)
        values from the JWK, and then serializes it to PEM format.

        Args:
            jwk (dict): The JWK containing the public key components (n, e).

        Returns:
            bytes: The RSA public key in PEM format.
        """
        logger.log("3. Validating jwk by 'RSAPublicNumbers' class\n", "i")
        return (
            RSAPublicNumbers(
                n=self.decode_value(jwk["n"]), e=self.decode_value(jwk["e"])
            )
            .public_key(default_backend())
            .public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo,
            )
        )

    def get_kid(self, token):
        """
        Extracts the Key ID (kid) from the JWT header.

        This method retrieves the unverified header from the JWT and returns the kid
        value, which is used to identify the public key for validating the token.

        Args:
            token (str): The JWT string from which to extract the kid.

        Returns:
            str: The Key ID (kid) extracted from the JWT header.

        Raises:
            InvalidAuthenticationToken: If the token format is invalid or the kid is missing.
        """
        logger.log(f"2. Getting kid from token:\n{token}\n", "i")
        try:
            headers = jwt.get_unverified_header(token)
        except jwt.exceptions.DecodeError as e:
            raise InvalidAuthenticationToken(
                f"invalid token format. {e.args[0]}"
            ) from e
        if not headers:
            raise InvalidAuthenticationToken("missing headers")
        try:
            return headers["kid"]
        except KeyError as e:
            raise InvalidAuthenticationToken("missing 'kid'") from e

    def get_jwk(self, kid):
        """
        Retrieves the JSON Web Key (JWK) corresponding to the given Key ID (kid).

        This method searches through the JWKS (JSON Web Key Set) to find the JWK
        that matches the provided kid.

        Args:
            kid (str): The Key ID used to identify the correct JWK.

        Returns:
            dict: The JWK dictionary containing the public key components.

        Raises:
            InvalidAuthenticationToken: If the kid is not recognized in the JWKS.
        """
        response = requests.get(os.getenv("JWKS_URL"), timeout=1000)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data.get("keys"), list):
                keys = data
            else:
                raise ValueError("JWKS URL did not return a list of keys")
        else:
            raise ConnectionError(f"Failed to fetch JWKS: HTTP {response.status_code}")

        if keys is None or "keys" not in keys:
            logger.log("JWKS is None or 'keys' not found in JWKS", "c")
            raise InvalidAuthenticationToken("Invalid JWKS configuration")
        else:
            logger.log("3. Validating keys from kid\n", "i")
            for jwk in keys.get("keys"):
                if jwk.get("kid") == kid:
                    return jwk
            # raise InvalidAuthenticationToken("'kid' not recognized")

    def get_public_key(self, token):
        """
        Retrieves the public key used to validate the JWT.

        This method extracts the Key ID (kid) from the JWT header, finds the corresponding
        JSON Web Key (JWK) from the JWKS (JSON Web Key Set), and converts it to an RSA
        public key in PEM format.

        Args:
            token (str): The JWT string from which to extract the public key.

        Returns:
            bytes: The RSA public key in PEM format.

        Raises:
            InvalidAuthenticationToken: If the token format is invalid or the kid is missing.
        """
        return self.rsa_pem_from_jwk(self.get_jwk(self.get_kid(token)))

    def validate_jwt(self, jwt_to_validate):
        """
        Validates the given JWT (JSON Web Token) using the public key.

        This method decodes the JWT, verifies its signature, and checks that the
        token's audience and issuer match the expected values.

        Args:
            jwt_to_validate (str): The JWT string to be validated.

        Returns:
            dict: The decoded JWT payload if the token is valid.

        Raises:
         InvalidAuthorizationToken: If the JWT is invalid, expired, or its signature is incorrect.
        """
        logger.log("4. DECODING JWT\n", "i")
        public_key = self.get_public_key(jwt_to_validate)

        try:
            decoded = jwt.decode(
                jwt_to_validate,
                public_key,
                verify=True,
                algorithms=["RS256"],
                audience=self._Auth__valid_audiences,
                issuer=self._Auth__issuer,
            )
            logger.log("5. Decoded: returning info\n", "i")
            return decoded
        except Exception as e:
            raise InvalidAuthorizationToken(e.args[0]) from e

    def obtain_user_info(self, access_token: str) -> Dict:
        """
        Get the auth 'user' from a specific Azure endpoint.

        Args:
            access_token (str): AcessToken given by Azure auth.

        Raises:
         InvalidAuthorizationToken: invalid AccessToken format.

        Returns:
            Dict: 'user' info.
        """
        headers = {"Authorization": access_token}
        logger.log("1. Making call at Azure\n", "i")
        response_raw = requests.get(self.__url_graph, headers=headers, timeout=1000)
        logger.log("2. Loading response\n", "i")
        response = json.loads(response_raw.content)
        if "error" in response:
            logger.log(f"Error from azure call\nStack:\n{traceback.format_exc()}\n")
            raise InvalidAuthorizationToken(response["error"]["code"])

        logger.log("3. Returning  response\n", "i")
        return response

    def prepare_db_user(self, user_info: dict) -> Dict:
        """
        Builds the 'user' info according to the DB 'User' model.

        Args:
            user_info (dict): auth 'user'.

        Returns:
            Dict: 'user' dict model.
        """
        mail = user_info["user_email"]
        coc = Coc.exists_by_engagement_partner_email(mail)
        if coc:
            role = UserResources.APPROVER.name.lower()
        elif mail.startswith("a-") or "admin" in mail:
            role = UserResources.ADMIN.name.lower()
        else:
            role = UserResources.CONSUMER.name.lower()
        if len(user_info["name"].split(", ")) == 2:
            surname, name = user_info["name"].split(", ")
        else:
            name = user_info["name"]
            surname = ""
        return {
            "email": mail,
            "name": name,
            "role": role,
            "surname": surname,
            "residual_credits": UserResources[role.upper()].value["residual_credits"],
            "residual_requests": UserResources[role.upper()].value["residual_requests"],
            "user_id": mail.split("@", 1)[0],
            "azure_id": user_info["oid"],
        }

    def refresh_user_resources(self, user_resources: UserResources) -> None:
        """
        Handles the logic according to which if an 'user' has updated info
        (coming from Azure) or the access is made after 30 days, related DB 'user'
        attributes are going to be updated as consequence.

        Args:
            user (User): 'user' model instance.
            user_resources (UserResources): user_resources of the auth 'user'.
        """
        if isinstance(self.auth_user.last_access, str):
            last_access = datetime.fromisoformat(self.auth_user.last_access)
        else:
            last_access = self.auth_user.last_access

        last_access_tzinfo = (
            last_access.replace(tzinfo=self._now.tzinfo) if last_access else self._now
        )

        if (self._now.year != last_access_tzinfo.year) or (
            self._now.month != last_access_tzinfo.month
        ):
            logger.log("Refreshing monthly user resources..\n", "i")
            self.auth_user.residual_credits = Decimal(
                user_resources.value["residual_credits"]
            )
            self.auth_user.residual_requests = Decimal(
                user_resources.value["residual_requests"]
            )

        logger.log("Updating user last access time..\n", "i")
        self.auth_user.last_access = self._now
        self._session.query(User).filter(User.id == self.auth_user.id).update(
            {"last_access": self._now}, synchronize_session="fetch"
        )
        self._session.commit()


class UserAuth(Auth):
    """
    Executes the auth.

    Args:
        Auth (_type_): authentication/authorization handler.
    """

    def __init__(self, request: Request) -> None:
        super().__init__(request)
        authconfig.is_active = True
        self.ttl = None

    def get_auth_info(self) -> Tuple[User, Any, str, int]:
        """
        Return the user information, cache client, session cookie, and TTL.

        If the TTL is calculated based on the token expiration, it is used. Otherwise,
        a default TTL of 3600 seconds (1 hour) is assigned.
        """
        self.ttl = self.ttl if self.ttl is not None else 3600
        return self.auth_user, self.cache, self.session_cookie, self.ttl
