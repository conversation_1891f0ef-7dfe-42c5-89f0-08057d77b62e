"""
Module for initializing default user accounts in a development environment.
This module fetches and logs the default admin, approver, and consumer users from the database.
It only runs in a development environment and skips execution if certain Flask DB commands
are detected.
"""

import sys

from sqlalchemy.exc import NoResultFound, SQLAlchemyError

from app.config import AuthConfig, logger
from app.extensions import session
from app.models.user import User

authconfig = AuthConfig()

# Database commands that should bypass seeder initialization
db_commands = [
    "branches",
    "current",
    "downgrade",
    "edit",
    "heads",
    "history",
    "init",
    "list-templates",
    "merge",
    "migrate",
    "revision",
    "seed",
    "show",
    "stamp",
    "upgrade",
]

# Check for default seed users
user_checks = [
    ("ADMIN_USER", "Admin User", authconfig.ADMIN_ID),
    ("APPROVER_USER", "Approver User", authconfig.APPROVER_ID),
    ("CONSUMER_USER", "Consumer User", authconfig.CONSUMER_ID),
]

if authconfig.dev_env and not any(cmd in " ".join(sys.argv) for cmd in db_commands):
    try:
        for user_attr, user_type, user_id in user_checks:
            setattr(authconfig, user_attr, authconfig.get_user(User, session, user_id))
            USER = getattr(authconfig, user_attr)
            if isinstance(USER, User) and USER.id:
                logger.log(f"Found {user_type}: {USER}", "i")
            else:
                logger.log(f"{user_type} not found or not populated properly.", "w")
    except NoResultFound:
        logger.log("No user found for one of the default user roles.", "w")
    except SQLAlchemyError as e:
        logger.log(f"A database error occurred: {e}", "c")
