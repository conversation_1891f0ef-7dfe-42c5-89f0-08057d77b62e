"""
Utility functions and classes for validating and processing data related to companies, persons,
and documents.

This module contains utility functions that validate tax codes, VAT codes, and entities
(company, person, or document). It also provides helper functions to build request headers,
manage action types, generate UUIDs, and fetch the current datetime in a
specific timezone (Europe/Rome).

Functions:
    - is_valid_tax_code: Validates the format of an Italian tax code.
    - is_valid_vat_code: Validates the format of an Italian VAT number.
    - build_headers_params: Extracts and builds header parameters from request or keyword arguments.
    - _compose_err_msg: Composes standardized error messages.
    - lock_action: Determines the action type (buy, rebuy, or assign) based on request parameters.
    - create_guid: Generates a globally unique identifier (UUID) ensuring it
    doesn't start with a leading zero.
    - utcnow: Returns the current date and time in the Europe/Rome timezone.

Classes:
    - Entities (Enum): Represents entity types (person, company) and provides
    utility methods for entity validation and related operations.

Modules and Packages Imported:
    - re: Provides support for regular expressions.
    - datetime: Handles date and time-related functions.
    - enum: Supports enumerations, used for defining constants.
    - http: Provides HTTP status codes for error responses.
    - typing: Used for type hints and type unions.
    - uuid: Generates and handles UUIDs.
    - pytz: Allows timezone handling and conversions.
    - pyvat: Validates VAT numbers for European countries, specifically for Italy in this module.
    - app.enums.request.action: Contains action types for different request
    operations (buy, rebuy, assign).
    - app.enums.utils: Contains response-related utilities for standardized responses.
"""

import re
from datetime import datetime
from http import HTTPStatus
from uuid import UUID, uuid4

import pytz
import pyvat

from app.enums.request.action import ActionEnum
from app.enums.utils import Response


def is_valid_tax_code(tax_code: str) -> bool:
    """
    Validates if the given tax code conforms to the Italian tax code format.

    Args:
        tax_code (str): The tax code to be validated.

    Returns:
        bool: True if the tax code is valid, False otherwise.
    """
    return bool(
        re.fullmatch(
            "^[a-zA-Z]{6}[0-9]{2}[abcdehlmprstABCDEHLMPRST]{1}[0-9]{2}([a-zA-Z]{1}[0-9]{3})[a-zA-Z]{1}$",
            tax_code,
        )
    )


def is_valid_vat_code(vat_code: str) -> bool:
    """
    Validates if the given VAT code is a valid Italian VAT number.

    Args:
        vat_code (str): The VAT code to be validated.

    Returns:
        bool: True if the VAT code is valid, False otherwise.
    """
    return bool(pyvat.is_vat_number_format_valid(vat_code, country_code="IT"))


def build_headers_params(resource_class: object, kwargs: dict, params: tuple) -> dict:
    """
    Builds a dictionary of header parameters from a resource or given keyword arguments.

    Args:
        resource_class (object): The resource class that may contain request headers.
        kwargs (dict): Additional keyword arguments to extract parameters from.
        params (tuple): Tuple of parameter names to extract.

    Returns:
        dict: Dictionary containing the extracted header parameters.
    """
    _d = dict()
    if hasattr(resource_class, "request"):
        for param in params:
            _d = {**_d, param: resource_class.request.headers[param]}
        return _d

    for param in params:
        _d = {**_d, param: kwargs[param]}
    return _d


def _compose_err_msg(msg: str) -> tuple:
    """
    Composes an error message in a standardized format.

    Args:
        msg (str): The error message.

    Returns:
        tuple: A tuple containing the error details and an HTTP status code.
    """
    err = {
        "source": Response.api.value,
        "message": msg,
        "error": HTTPStatus.NOT_FOUND,
    }
    return (err, HTTPStatus.NOT_FOUND, "close_session")


def lock_action(kwargs: dict) -> "ActionEnum":
    """
    Determines the action type (buy, rebuy, or assign) based on the provided keyword arguments.

    Args:
        kwargs (dict): Dictionary of keyword arguments to check for action-related keys.

    Returns:
        ActionEnum: The action type determined from the keyword arguments.
    """
    action = ActionEnum.BUY.value
    if kwargs.get("rebuy") is not None:
        action = ActionEnum.REBUY.value
    elif kwargs.get("assignable"):
        action = ActionEnum.ASSIGN.value
    return action


def create_guid() -> UUID:
    """
    Generates a UUID, ensuring that it doesn't start with a leading zero.

    Returns:
        UUID: A new UUID value.
    """
    # Work around UUIDs with leading zeros: https://github.com/tiangolo/sqlmodel/issues/25
    val = uuid4()
    while val.hex[0] == "0":
        val = uuid4()
    return val


def utcnow() -> datetime:
    """
    Returns the current date and time in the Europe/Rome timezone.

    Returns:
        datetime: The current datetime in the Europe/Rome timezone.
    """
    return datetime.now(pytz.timezone("Europe/Rome"))
