"""
This module provides utility functions and classes for database management,
email services, seeding operations, and role-based access control within the application.

It includes:
- Database session management and transaction handling.
- UUID generation and registration.
- SQLAlchemy ORM model management.
- Custom decorators for managing transactions and enforcing role-based access.

Classes:
    Model:
        Manages SQLAlchemy models, providing dynamic access to database tables based on model names.

Functions:
    manage_transaction(func) -> Any:
        Decorator that handles database transactions, ensuring consistency during operations.

    uuid() -> UUID:
        Generates a new UUID4 identifier.

    higher_level_role_required(func) -> Any:
        Decorator that enforces higher-level role requirements for accessing specific API endpoints.
"""

import os
import traceback
from http import HTTPStatus
from typing import Any
from uuid import UUID, uuid4

import psycopg2.extras
from flask_mail import Mail
from flask_seeder import FlaskSeeder
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import create_engine
from sqlalchemy.orm.session import Session

from app.config import logger

from .enums.user.resources import UserResources
from .enums.utils import Response

engine = create_engine(
    os.getenv(
        "SQLALCHEMY_DATABASE_URI",
    ),
    echo=True,
)
session = Session(bind=engine, expire_on_commit=False)


psycopg2.extras.register_uuid()


# Create SQLAlchemy instance
db = SQLAlchemy()
# Create email instance
email = Mail()
# Create seeder instance
seeder = FlaskSeeder()


class Model:
    """
    A utility class for managing and accessing SQLAlchemy models dynamically.

    This class initializes with a model name and allows for retrieving the corresponding
    SQLAlchemy model class associated with that name.

    Attributes:
        db_ (SQLAlchemy): The SQLAlchemy instance managing the database connection.
        models_ (dict): A dictionary mapping model names to their corresponding SQLAlchemy classes.

    Methods:
        get_model() -> object:
            Retrieves the SQLAlchemy model class associated with the specified model name.
    """

    db_ = db

    models_ = dict()

    def __init__(self, model_name: str) -> None:
        self.models_ = {
            mapper.class_.__name__: mapper.class_
            for mapper in db.Model.registry.mappers
        }
        self.name = model_name

    def get_model(self) -> object:
        """
        Retrieves the SQLAlchemy model class based on the initialized model name.

        Returns:
            object: The SQLAlchemy model class corresponding to the `model_name`
            provided during initialization.

        Raises:
            KeyError: If the specified model name does not exist in the registered models.
        """
        return self.models_[self.name]


def manage_transaction(func) -> Any:
    """
    Handles the operations during the transaction: avoids
    any possible inconsistency due to creation/modification/deletion
    of DB data
    """
    try:
        error_: str = "error"

        def wrapper(*args, **kwargs):
            def remove_(tuple_: tuple, target: Any) -> tuple:
                idx = tuple_.index(target)
                return tuple_[:idx] + tuple_[idx + 1 :]

            try:
                rv = func(*args, **kwargs)
                if isinstance(rv, tuple):
                    if error_ in rv[0]:
                        session.rollback()
                        session.close()
                        rv = (
                            remove_(rv, "close_session")
                            if "close_session" in rv
                            else rv
                        )
                        return rv

                session.commit()
                if "close_session" in rv:
                    session.close()
                    rv = remove_(rv, "close_session")
                return rv
            except Exception as e:
                session.rollback()
                session.close()

                # Logs
                logger.log(
                    f"NOT HANDLED ERROR:\n{e}\nSTACK:\n{traceback.format_exc()}\n", "c"
                )
                return (
                    {
                        "source": "Server",
                        "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                        "message": "Please contact the administrator",
                        "details": e.args[0],
                    },
                    HTTPStatus.INTERNAL_SERVER_ERROR,
                )

        return wrapper
    except Exception as e:
        return (
            {
                "source": "Server",
                "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                "message": "Please contact the administrator",
                "details": e.args[0],
            },
            HTTPStatus.INTERNAL_SERVER_ERROR,
        )


def uuid() -> UUID:
    """
    Generates a new uuid4 `id`.

    Returns:
        UUID: new uuid4.
    """
    guid = uuid4()
    while guid.hex[0] == 0:
        guid = uuid4()
    return guid


def higher_level_role_required(func) -> Any:
    """
    Special permissions required ("approver" role) is
    required to consume specifics APIs.
    """

    def wrapper(*args, **kwargs):
        kwargs["user_role"] = args[0].guest.role

        if kwargs["user_role"] not in [
            UserResources.APPROVER.name.lower(),
            UserResources.ADMIN.name.lower(),
        ]:
            logger.log(f"NOT AUTHORIZED\nSTACK:\n{traceback.format_exc()}\n", "w")
            return (
                {
                    "source": Response.api.value,
                    "details": "API accessable by users with special permissions only",
                },
                HTTPStatus.UNAUTHORIZED,
            )
        return func(*args, **kwargs)

    return wrapper
