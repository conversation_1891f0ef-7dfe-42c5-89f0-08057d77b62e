"""
Module defining action types for document requests.

Provides an enumeration of possible actions that can be performed on documents:
- buy: Purchase a new document
- rebuy: Purchase an updated version of a document
- assign: Assign a document to a user
"""

from enum import Enum


class ActionEnum(Enum):
    """
    Enumeration of valid actions for document requests.

    Attributes:
        BUY: Action to purchase a new document
        REBUY: Action to purchase an updated version
        ASSIGN: Action to assign a document to a user
    """

    BUY: str = "buy"
    REBUY: str = "rebuy"
    ASSIGN: str = "assign"

    @classmethod
    def __set_members__(cls) -> list:
        return [enum.value for enum in cls]
