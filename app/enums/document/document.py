"""
This module defines enums for handling document types and their associated prices in the system.

- `DocsPricesEnum`: Contains the different document types and their respective prices.
- `ServiceDocumentType`: Contains service document types and provides a method for validating them.
"""

from enum import Enum
from http import HTTPStatus
from typing import Union

from app.utils import Response


class DocsPricesEnum(Enum):
    """
    Enum representing the prices of various document types.

    Attributes:
        BALANCE (float): Price for balance document.
        COMPANY_CARD (float): Price for company card document.
        FINANCIAL_STATEMENT (float): Price for financial statement document.
        LEGAL_PROCEDURES_DEED (float): Price for legal procedures deed document.
        REPORT_ADVISOR (float): Price for report advisor document.
        REPORT_EXPERT (float): Price for report expert document.
        REPORT_COMPLETE (float): Price for a complete report.
        REPORT_STANDARD (float): Price for a standard report.
        REPRESENTATIVE_CARD_COMPLETE (float): Price for a complete representative card.
        REPRESENTATIVE_CARD_CURRENT (float): Price for a current representative card.
        REPRESENTATIVE_CARD_HISTORY (float): Price for a representative card with history.
        SHAREHOLDERS (float): Price for shareholders document.
        SHARES (float): Price for shares document.
        VISURA (float): Price for a visura document.
        VISURA_HISTORIC (float): Price for a historic visura document.
    """

    BALANCE: float = 2.88
    COMPANY_CARD: float = 2.1
    FINANCIAL_STATEMENT: float = 3.5
    LEGAL_PROCEDURES_DEED: float = 3.08
    REPORT_ADVISOR: float = 25.2
    REPORT_EXPERT: float = 21.42
    REPORT_COMPLETE: float = 2.32
    REPORT_STANDARD: float = 6.16
    REPRESENTATIVE_CARD_COMPLETE: float = 7.7
    REPRESENTATIVE_CARD_CURRENT: float = 4.34
    REPRESENTATIVE_CARD_HISTORY: float = 6.1
    SHAREHOLDERS: float = 4.37
    SHARES: float = 4.37
    VISURA: float = 3.43
    VISURA_HISTORIC: float = 3.78

    @classmethod
    def __set_members__(cls) -> list:
        """
        Class method to return a list of enum values.

        Returns:
            list: A list of prices corresponding to the enum members.
        """
        return [enum.value for enum in cls]


class ServiceDocumentType(Enum):
    """Enum for document types with mapping to supplier values"""

    BALANCE = "balance"
    COMPANY_CARD = "company_card"
    FINANCIAL_STATEMENT = "financial_statement"
    LEGAL_PROCEDURES_DEED = "legal_procedures_deed"
    REPORT_ADVISOR = "report_advisor"
    REPORT_EXPERT = "report_expert"
    REPORT_COMPLETE = "report_complete"
    REPORT_STANDARD = "report_standard"
    REPRESENTATIVE_CARD_COMPLETE = "representative_card_complete"
    REPRESENTATIVE_CARD_CURRENT = "representative_card_current"
    REPRESENTATIVE_CARD_HISTORY = "representative_card_history"
    SHAREHOLDERS = "shareholders"
    SHARES = "shares"
    VISURA = "visura"
    VISURA_HISTORIC = "visura_historic"

    def get_supplier_value(self) -> str:
        """Get the corresponding supplier value for this document type"""
        supplier_mapping = {
            "company_card": "SilverCribis",
            "report_advisor": "RptAdvisor",
            "report_complete": "RptComplete",
            "report_expert": "RptExpert",
            "report_standard": "RptStandard",
            "representative_card_complete": "SKC",
            "representative_card_current": "SKP",
            "representative_card_history": "SKS",
            "visura": "VATTUPDF",
            "visura_historic": "VSTORPDF",
        }
        return supplier_mapping.get(self.value, self.value)

    @classmethod
    def check(cls, service_type: str, doc_type: str) -> Union[dict, None]:
        """
        Class method to check if the provided document type is valid for a given service type.

        Args:
            service_type (str): The service type to check.
            doc_type (str): The document type to check.

        Returns:
            Union[dict, None]: A dictionary containing error information if invalid, or None if valid.
        """
        try:
            cls.__annotations__[service_type]
            if doc_type != cls[service_type].value:
                return {
                    "source": Response.api.value,
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    "message": f"'{doc_type}' is not a valid document type member of '{service_type}'",
                }
        except KeyError:
            return {
                "source": Response.api.value,
                "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                "message": f"'{service_type}' is not a valid service type member",
            }
