from enum import Enum
from typing import Any

from app.validate import validate_company, validate_person


class Entities(Enum):
    """
    Enum representing the types of entities (person or company) and provides
    utility methods to handle validation and related operations.

    Attributes:
        person (str): Represents a person entity.
        company (str): Represents a company entity.
    """

    person: str = "person"
    company: str = "company"
    __func = {"person": validate_person, "company": validate_company}
    __params = {"person": "person_id", "company": "company_id"}
    __doc_relation = {"person": "tax_code", "company": "vat_code"}
    __tables_conversion = {"person": "tax_code", "company": "company_code"}

    def func(self) -> Any:
        """
        Returns the validation function for the given entity type.

        Returns:
            Any: The validation function for the entity.
        """
        return self.__func.value[self.value]

    def params(self) -> str:
        """
        Returns the parameter key used to reference the entity in requests.

        Returns:
            str: The parameter key for the entity.
        """
        return self.__params.value[self.value]

    def link_doc(self) -> str:
        """
        Returns the document-related attribute for the entity.

        Returns:
            str: The document-related attribute (tax code for person, VAT code for company).
        """
        return self.__doc_relation.value[self.value]

    def convert_domain_name(self) -> str:
        """
        Returns the domain name conversion for the entity.

        Returns:
            str: The domain name conversion attribute.
        """
        return self.__tables_conversion.value[self.value]
