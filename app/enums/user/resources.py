from enum import Enum


class UserResources(Enum):
    CONSUMER: dict = {
        "residual_credits": 300,
        "residual_requests": 10,
    }
    APPROVER: dict = {
        "residual_credits": 600,
        "residual_requests": 0,
    }
    ADMIN: dict = {
        "residual_credits": 1500,
        "residual_requests": 0,
    }

    @classmethod
    def __set_members__(cls) -> dict:
        return {el.name.lower(): el.value for el in cls}

    @classmethod
    def get_all_roles(cls) -> list:
        return [role.name.lower() for role in cls]
