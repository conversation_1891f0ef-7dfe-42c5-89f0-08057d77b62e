"""
This module defines the `MailTemplate` enum, which encapsulates
different types of email templates.

The enum members in `MailTemplate` contain the subject, message, and required variables for
each email type. These templates are used by the mail-sending logic in the application
to ensure that emails are generated and sent consistently and correctly across different
parts of the system.
"""

from enum import Enum


class MailTemplate(Enum):
    """
    Enum class representing different types of email templates.

    Each enum member contains:
    - `subject`: A template string for the email subject, with placeholders for variables.
    - `message`: A template string for the email body, with placeholders for variables.
    - `variables`: A list of required variables that should be substituted in the subject
    and message templates.
    """

    WELCOME = {
        "subject": "Benvenuto in Spartax, {user_name}!",
        "message": (
            "Ciao {user_name},<br><PERSON>razie per esserti registrato sulla nostra piattaforma. "
            "Il tuo account è ora attivo."
        ),
        "variables": ["user_name"],
    }
    NEW_DOCUMENT = {
        "subject": "Hai acquistato un nuovo documento",
        "message": (
            "Ciao {user_name},<br><PERSON><PERSON><PERSON> per aver usato Spartax per acquistare il tuo documento. "
            "I dettagli del tuo acquisto sono:."
        ),
        "variables": ["user_name"],
    }
    NEW_REQUEST = {
        "subject": "Hai richiesto l'acquisto di un documento",
        "message": (
            "Ciao {user_name},<br>Hai richiesto un nuovo documento.I dettagli del documento richiesto sono:<br><br>"
            "- Azienda: {company}<br>"
            "- P.iva: {tax_code}<br>"
            "- Documento: {document_type}<br>"
            "- Centro di costo: {coc}<br>"
            "- Engagement partner: {approver_name}<br>"
            "- Prezzo: {price}<br>"
        ),
        "variables": [
            "approver_name",
            "coc",
            "company",
            "document_type",
            "price",
            "tax_code",
            "user_name",
        ],
    }
    NEW_REQUEST_RECEIVED = {
        "subject": "{user_name} ti ha inviato una richiesta di acquisto",
        "message": (
            "Ciao {approver_name},<br>Hai ricevuto una nuova richiesta per l'approvazione di un documento. Qua sotto trovi i dettagli della richiesta:<br><br>"
            "- Nome: {user_name}<br>"
            "- Cognome: {user_surname}<br>"
            "- Centro di costo: {coc}<br>"
            "- Azienda: {document_company}<br>"
            "- Documento: {document_selected}<br>"
            "- Prezzo: {price} EUR"
        ),
        "variables": [
            "approver_name",
            "user_name",
            "user_surname",
            "coc",
            "document_company",
            "document_selected",
            "price",
        ],
    }
    FIRST_REQUEST_RECEIVED = {
        "subject": "Approva la tua prima richiesta di acquisto",
        "message": (
            "Ciao {approver_name},<br><br>Un utente ha selezionato il centro di costo {coc} per il "
            "quale sei selezionato come approvatore.<br><br>"
            "Dettagli della richiesta:<br>"
            "- Azienda: {document_company}<br>"
            "- Documento: {document_selected}<br>"
            "- Prezzo: {price} EUR<br>"
            "- Codice fiscale: {tax_code}<br>"
            "- Richiesto da: {user_name} {user_surname}"
        ),
        "variables": [
            "approver_name",
            "coc",
            "document_company",
            "document_selected",
            "price",
            "tax_code",
            "user_name",
            "user_surname",
        ],
    }
    REQUEST_ACCEPTED = {
        "subject": "La tua richiesta è stata approvata",
        "message": (
            "Ciao {user_name},<br>La tua richiesta di acquisto del documento associato al centro di costo {coc} è stata approvata."
        ),
        "variables": ["coc", "user_name"],
    }
    REQUEST_REJECTED = {
        "subject": "La tua richiesta è stata rifiutata",
        "message": (
            "Ciao {user_name},<br>La tua richiesta di acquisto del documento associata al centro di costo {coc} è stata rifiutata."
        ),
        "variables": ["coc", "user_name"],
    }
    SECONDARY_DOCUMENT_TYPE = {
        "subject": "Un utente ha richiesto un documento secondario",
        "message": (
            "Un documento secondario è stato richiesto con le seguenti informazioni:<br><br>"
            "- Nome: {user_name}<br>"
            "- Cognome: {user_surname}<br>"
            "- Email: {user_email}<br>"
            "- ID: {user_id}<br>"
            "- Ultimo accesso: {user_last_access}<br>"
            "- Ruolo: {user_role}<br><br>"
            "Informazioni sul documento:<br>"
            "- Azienda: {document_company}<br>"
            "- Persona: {document_person}<br>"
            "- Selezionato: {document_selected}<br>"
            "- WBS Selezionato: {document_selectedWbs}"
        ),
        "variables": [
            "user_name",
            "user_surname",
            "user_email",
            "user_id",
            "user_last_access",
            "user_role",
            "document_company",
            "document_person",
            "document_selected",
            "document_selectedWbs",
        ],
    }
    DEFAULT_COC = {
        "subject": "Un utente ha selezionato il centro di costo fittizio",
        "message": (
            "Ciao ,<br><br>Il centro di costo fittizio è stato selezionato:<br><br>"
            "- Centro di costo: {coc}"
        ),
        "variables": ["coc"],
    }
    FEEDBACK = {
        "subject": "Un utente ha inviato un messaggio",
        "message": (
            "L'utente <b>{user_id}</b> con ID: <b>{id}</b> ha inviato il seguente messaggio:<br><br>"
            "{content}"
            "<br><br>{timestamp}"
        ),
        "variables": ["content", "id", "timestamp", "user_id"],
    }

    def get_subject(self):
        """
        Retrieves the subject template string for the email.

        Returns:
            str: The subject template with placeholders for variables.
        """
        return self.value["subject"]

    def get_message(self):
        """
        Retrieves the message template string for the email.

        Returns:
            str: The message template with placeholders for variables.
        """
        return self.value["message"]

    def get_variables(self):
        """
        Retrieves the list of required variables for the email template.

        Returns:
            list: A list of variable names that need to be substituted in the subject and message
            templates.
        """
        return self.value["variables"]
