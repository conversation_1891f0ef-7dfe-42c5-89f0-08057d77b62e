"""
Module for handling document rebuy operations.

This module provides functionality to rebuy existing documents, create new requests,
and handle document updates. It includes support for both company and person entities,
and manages the interaction with external services and Azure storage.
"""

from http import HTTPStatus
from typing import Union
from uuid import UUID

from app.config import logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.request.action import ActionEnum
from app.enums.request.status import StatusEnum
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.person import Person
from app.models.request import Request
from app.resource_access import BaseResource
from app.utils import build_headers_params, utcnow
from app.validate import validate_document

from . import Services


class Rebuy(BaseResource):
    """
    Rebuy class.
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None
    access_headers: tuple = ("coc",)
    access_body: bool = True

    @manage_transaction
    def patch(self, document_id: UUID, **kwargs) -> tuple:
        """
        Rebuy an existing document adding a new 'request' record
        and updating the 'file_url' attribute of the document.

        Args:
            document_id (UUID): guid of the related existing document.

        Returns:
            tuple: response.
        """
        logger.log("STARTING PROCESS OF REBUYING:\n", "i")
        logger.log("1. BUILDING HEADERS PARAMS...\n", "i")
        vars_ = build_headers_params(self, kwargs, self.access_headers)
        coc_ = vars_["coc"]

        logger.log("2. VALIDATING DOCUMENT BY DOCUMENT ID...\n", "i")
        document: Union[Document, tuple] = validate_document(document_id)
        if isinstance(document, tuple):
            logger.log("DOCUMENT NOT FOUND INTO THE DB\n", "w")
            return document

        logger.log("3. GETTING RELATED SERVICE AND PREPARE THE CALL...\n", "i")
        _service_type = getattr(Services, document.type.upper())["name"]
        service = Services(
            _service_type, document_type=document.type, document_id=document.id
        )
        if hasattr(service, "err"):
            logger.log(
                f"SOMETHING WENT WRONG IN THE PROCESS OF REBUY:\n{service.err}\n", "c"
            )
            return (service.err, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

        logger.log(
            "4. BUILDING INFO TO PASS AS PARAMS TO THE RELATED SERVICE TO CALL...\n",
            "i",
        )
        base_info = {
            "document_type": getattr(ServiceDocumentType, document.type.upper()).value
        }
        response_updatable = (
            {"vat_code": document.company_code}
            if document.company_code is not None
            else {"tax_code": document.tax_code}
        )
        _obj = (
            {"attr": "company_id"}
            if document.company_code is not None
            else {"attr": "person_id"}
        )
        _obj = {**response_updatable, **_obj}
        _cls = Company if _obj.get("vat_code") is not None else Person
        entity = _cls.query_by_criteria(
            [getattr(_cls, list(_obj.keys())[0]) == list(_obj.values())[0]]
        )
        entity: _cls = entity[0]
        _obj[_obj["attr"]] = entity.id
        if kwargs.get("self.guest") is not None and not hasattr(self, "guest"):
            setattr(self, "guest", kwargs["self.guest"])
        elif hasattr(self, "guest") and kwargs.get("self.guest") is None:
            kwargs["self.guest"] = self.guest
        kwargs["rebuy"] = True

        if kwargs.get("by_approval"):
            kwargs["user_request"] = kwargs["request_obj"]
            kwargs["request_id"] = kwargs["request_obj"].id

        now = utcnow()
        request__ = kwargs["request__"] if "request__" in kwargs else dict()
        _kwargs = {**request__, **_obj, **base_info, **kwargs}
        if "document_id" not in _kwargs:
            _kwargs["document_id"] = document.id
        _kwargs["Id"] = self.guest.user_id

        logger.log(f"GUEST:\n{self.guest}\nPARAMS BY kwargs:\n{_kwargs}", "i")
        service_call = service(guest=self.guest, **_kwargs)
        if isinstance(service_call, tuple):
            logger.log(
                f"SOMETHING WENT WRONG IN THE PROCESS OF REBUY AFTER THE SERVICE \
                    HAS BEEN CALLED:\n{service_call}\n",
                "c",
            )
            return (*service_call, "close_session")

        logger.log("5 UPLOADING ON AZURE CONTAINER...\n", "i")
        azurl = service_call["azstorage"].upload(
            document,
            service_call["doc"],
            document.type,
            to_unzip=(
                True
                if getattr(ServiceDocumentType, document.type).value
                == ServiceDocumentType.VISURA.value
                else False
            ),
            overwrite=True,
        )
        document.file_url = azurl
        document.updated_at = utcnow()
        document.update()

        if kwargs.get("by_approval") is None:
            logger.log("6 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i")
            request = Request(
                request_timestamp=now,
                response_timestamp=utcnow(),
                document_id=document.id,
                purchase=False,
                user_id=self.guest.user_id,
                coc=coc_,
                action=ActionEnum.REBUY.value,
                **{_obj["attr"]: entity.id},
            )
            request.save()

        elif kwargs.get("by_approval"):
            logger.log("6 PREPARING PARAMNS TO UPDATE REQUEST UNDER APPROVAL...\n", "i")
            kwargs["request_obj"].response_timestamp = utcnow()
            kwargs["request_obj"].purchase = True
            kwargs["request_obj"].status = StatusEnum.ACCEPTED.value
            kwargs["request_obj"].bought_by = self.guest.user_id
            kwargs["request_obj"].document_id = document.id
            logger.log(f"REQUEST OBJECT UPDATED:\n{kwargs['request_obj']}\n", "i")

        residual_credits = dict()
        if document.type != getattr(Services, document.type).get("name"):
            logger.log("7.REMOVING CREDITS IF NEEDED...\n", "i")
            residual_credits = {
                "residual_credits": self.guest.reduce_resources(
                    getattr(DocsPricesEnum, document.type.upper()).value,
                    "residual_credits",
                )
            }

        response = {
            "file_url": document.file_url,
            "id": str(document.id),
            "type": document.type,
            **response_updatable,
            **residual_credits,
        }
        httpstatus = HTTPStatus.ACCEPTED

        logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
        return (response, httpstatus, "close_session")
