from http import HTTPStatus
from typing import List, Union
from uuid import UUID

from app.config import Supplier, logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.request import Request
from app.resource_access import BaseResource
from app.storage.azstorage import AZStorage
from app.utils import build_headers_params, lock_action, utcnow
from app.validate import validate_company


class CompanyCardResource(BaseResource):
    """Endpoint for creating a new CompanyCard"""

    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = (DocsPricesEnum.COMPANY_CARD.name,)
    doc_type_name_referance_: bool = True
    access_headers: tuple = ("coc",)

    @manage_transaction
    def get(self, company_id: UUID, by_approval: bool = False, **kwargs) -> tuple:
        """
        Endpoint returning a CompanyCard Report specified by a vat_code and a product_id

        parameters:
          - name: company_id
            in : path
            type : UUID
            required : true

        responses:
          200 :
            description: the report is present in the database
          201 :
            description : the report is obtained after querying external data supplier db
          404 :
            description : no companycards are available on external data supplier db
          422 :
            description : the input is not valid
        """
        try:
            logger.log("STARTING PROCESS TO SAVE COMPNY CARD DOC:\n", "i")
            logger.log(f"1. INTERPRETING ACTIONS FROM kwargs:\n{kwargs}...\n", "i")

            action = lock_action(kwargs)
            rebuy = kwargs.get("rebuy")
            if by_approval or rebuy:
                setattr(self, "guest", kwargs["self.guest"])

            logger.log("2. VALIDATING COMPANY BY COMPANY ID...\n", "i")
            company: Union[Company, tuple] = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log("4. BUILDING HEADERS PARAMS...\n", "i")
            vars_ = build_headers_params(self, kwargs, self.access_headers)
            coc_ = vars_["coc"]
            expire = "close_session" if not by_approval else None
            now = utcnow()

            company_card: List[Document] = (
                Document.get_by_company_code_and_document_types(
                    company_code=company.vat_code,
                    document_types=[ServiceDocumentType.COMPANY_CARD.value],
                )
            )

            response = None
            httpstatus = None
            remove_credits = False
            residual_credits = dict()
            azstorage: AZStorage = AZStorage()

            logger.log("5. CHECKING IF DB DOCUMENT ALREADY ADDED INTO THE DB...\n", "i")
            if len(company_card) == 0 or rebuy:
                logger.log("5.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("company_card")
                a2a = supplier()

                logger.log("5.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.GetReportByTAXCode(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    ProductFormats="PDF",
                    ProductID=ServiceDocumentType("company_card").get_supplier_value(),
                    TAXCode=company.vat_code,
                )

                # Controllo che la risorsa sia stata correttamente estratta
                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if rebuy:
                    logger.log("5.3 STARTIG REBUY PROCESS...\n", "i")
                    return {
                        "doc": doc.BusinessReportAttachments.ProductAttachment[
                            0
                        ].Content,
                        "azstorage": azstorage,
                    }

                logger.log("5.4 SAVING NEW DOCUMENT OBJECT...\n", "i")
                company_card = Document(
                    company_code=company.vat_code,
                    type=ServiceDocumentType.COMPANY_CARD.value,
                )
                company_card.save()

                logger.log("5.5 UPLOADING ON AZURE CONTAINER...\n", "i")
                azurl = azstorage.upload(
                    company_card,
                    doc.BusinessReportAttachments.ProductAttachment[0].Content,
                    ServiceDocumentType.COMPANY_CARD.value,
                )
                company_card.file_url = azurl
                company_card.update()

                logger.log("5.6 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    result_code=doc.TransactionResponse.Result.Code,
                    document_id=company_card.id,
                    purchase=True,
                    user_id=self.guest.user_id,
                    bought_by=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.6.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = (
                    {
                        "file_url": company_card.file_url,
                        "id": str(company_card.id),
                        "type": company_card.type,
                        "vat_code": company_card.company_code,
                    }
                    if not by_approval
                    else request.as_dict()
                )
                httpstatus = HTTPStatus.CREATED
                remove_credits = True

            if response is None:
                logger.log(
                    "5.1 DOCUMENT ALREADY EXISTS: CREATING NEW REQUEST OBJECT TO SAVE...\n",
                    "i",
                )
                company_card: Document = company_card[0]

                logger.log("5.2 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    request_timestamp=now,
                    response_timestamp=utcnow(),
                    document_id=company_card.id,
                    purchase=False,
                    user_id=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.2.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = {
                    "file_url": azstorage.download(company_card),
                    "id": str(company_card.id),
                    "type": company_card.type,
                    "vat_code": company_card.company_code,
                }

                residual_credits = {
                    "residual_credits": float(round(self.guest.residual_credits, 2))
                }
                httpstatus = HTTPStatus.OK

            # Remove credits only if API response is success
            if remove_credits:
                logger.log("5.2 REMOVING CREDITS IF NEEDED...\n", "i")
                residual_credits = {
                    "residual_credits": self.guest.reduce_resources(
                        DocsPricesEnum.COMPANY_CARD.value, "residual_credits"
                    )
                }
            response = {**response, **residual_credits}

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus, expire)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
