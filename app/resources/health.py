"""
This module defines the HealthResource for the application, providing a minimal health check
endpoint to confirm that the server is running and responsive.

The main purpose of this endpoint is to serve as a heartbeat for monitoring services, such as
Azure, to verify that the application is online. The endpoint responds with a simple JSON message
and HTTP status code 200 (OK) if the application is running correctly. No authentication or
database access is required for this endpoint, making it lightweight and reliable.
"""

from http import HTTPStatus

from flask_restful import Resource


class HealthResource(Resource):
    """
    HealthResource is a simple API endpoint for monitoring the application's availability.

    This endpoint responds with a minimal JSON message and an HTTP status code, indicating
    the application’s current running status. It's designed for external monitoring tools to
    confirm the application is online. The response is intentionally lightweight to reduce
    server load during health checks.

    Methods:
        get: Returns a JSON response with a status message and HTTP 200 status code.
    """

    def get(self) -> tuple:
        """
        Health check endpoint to confirm that the application is running.

        Returns:
            tuple: A tuple containing a JSON response
            with a status message and the HTTP status code.

        Responses:
            200 OK: Health check successful, the application is running and responsive.
        """
        return {"status": "ok"}, HTTPStatus.OK
