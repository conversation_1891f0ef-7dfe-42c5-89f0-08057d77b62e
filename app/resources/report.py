from datetime import date
from http import HTTPStatus
from typing import Union
from uuid import UUID

from app.config import Supplier, logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.request import Request
from app.resource_access import BaseResource
from app.storage.azstorage import AZStorage
from app.utils import build_headers_params, lock_action, utcnow
from app.validate import validate_company


class ReportResource(BaseResource):
    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = (
        DocsPricesEnum.REPORT_ADVISOR.name,
        DocsPricesEnum.REPORT_EXPERT.name,
    )
    access_headers: tuple = ("coc",)

    @manage_transaction
    def get(
        self, company_id: UUID, document_type, by_approval: bool = False, **kwargs
    ) -> tuple:
        """
        Endpoint for getting a report on a company using as input its tax code

        parameters:
          - name: company_id
            in : path
            type : UUID
            required : true
          - name: document_type
            in : path
            type : string
            required :true

        responses:
          200 :
            description : report present in local db
          201 :
            description : report obtained via connection to external data supplier server
          422 :
            description : input is not valid
        """
        try:
            logger.log("STARTING PROCESS TO SAVE REPORT DOC:\n", "i")
            logger.log(f"1. INTERPRETING ACTIONS FROM kwargs:\n{kwargs}...\n", "i")

            action = lock_action(kwargs)
            rebuy = kwargs.get("rebuy")
            if by_approval or rebuy:
                setattr(self, "guest", kwargs["self.guest"])

            logger.log("2. VALIDATING COMPANY BY COMPANY ID...\n", "i")
            company: Union[Company, tuple] = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log("4. BUILDING HEADERS PARAMS...\n", "i")
            vars_ = build_headers_params(self, kwargs, self.access_headers)
            coc_ = vars_["coc"]
            expire = "close_session" if not by_approval else None
            now = utcnow()

            if document_type not in [
                ServiceDocumentType.REPORT_ADVISOR.value,
                ServiceDocumentType.REPORT_EXPERT.value,
            ]:
                data = {
                    "source": Response.api.value,
                    "message": f"'product_id' must be a value of set: '{set([ServiceDocumentType.REPORT_ADVISOR.value, ServiceDocumentType.REPORT_EXPERT.value])}'",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }
                return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

            report: Document = Document.get_company_report(
                company_code=company.vat_code,
                product_types=[ServiceDocumentType(document_type).name],
            )

            response = None
            httpstatus = None
            remove_credits = False
            residual_credits = dict()
            azstorage: AZStorage = AZStorage()

            logger.log("5. CHECKING IF DB DOCUMENT ALREADY ADDED INTO THE DB...\n", "i")
            if report is None or rebuy:
                logger.log("5.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("report")
                a2a = supplier(apply_settings=False)

                logger.log("5.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.GetReportByVATCode(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    ProductID=ServiceDocumentType(document_type).get_supplier_value(),
                    ProductFormats="PDF",
                    VATCode=company.vat_code,
                )

                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if rebuy:
                    logger.log("5.3 STARTIG REBUY PROCESS...\n", "i")
                    return {
                        "doc": doc.BusinessReportAttachments.ProductAttachment[
                            0
                        ].Content,
                        "azstorage": azstorage,
                    }

                logger.log("5.4 SAVING NEW DOCUMENT OBJECT...\n", "i")
                report = Document(
                    company_code=company.vat_code,
                    type=ServiceDocumentType(document_type).name,
                    year=date.today().year,
                )
                report.save()

                logger.log("5.5 UPLOADING ON AZURE CONTAINER...\n", "i")
                azurl = azstorage.upload(
                    report,
                    doc.BusinessReportAttachments.ProductAttachment[0].Content,
                    "report",
                )
                report.file_url = azurl
                report.update()

                logger.log("5.6 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    result_code=doc.TransactionResponse.Result.Code,
                    document_id=report.id,
                    purchase=True,
                    user_id=self.guest.user_id,
                    bought_by=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.6.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = (
                    {
                        "file_url": report.file_url,
                        "id": str(report.id),
                        "type": report.type,
                        "vat_code": report.company_code,
                    }
                    if not by_approval
                    else request.as_dict()
                )
                httpstatus = HTTPStatus.CREATED
                remove_credits = True

            if response is None:
                logger.log(
                    "5.1 DOCUMENT ALREADY EXISTS: CREATING NEW REQUEST OBJECT TO SAVE...\n",
                    "i",
                )
                logger.log("5.2 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    request_timestamp=now,
                    response_timestamp=utcnow(),
                    document_id=report.id,
                    purchase=False,
                    user_id=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.2.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = {
                    "file_url": azstorage.download(report),
                    "id": str(report.id),
                    "type": report.type,
                    "vat_code": report.company_code,
                }

                residual_credits = {
                    "residual_credits": float(round(self.guest.residual_credits, 2))
                }
                httpstatus = HTTPStatus.OK

            # Remove credits only if API response is success
            if remove_credits:
                logger.log("5.2 REMOVING CREDITS IF NEEDED...\n", "i")
                doc_type = ServiceDocumentType(document_type).name.upper()
                residual_credits = {
                    "residual_credits": self.guest.reduce_resources(
                        getattr(DocsPricesEnum, doc_type).value,
                        "residual_credits",
                    )
                }
            response = {**response, **residual_credits}

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus, expire)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
