"""Module for managing document services and their configurations.

This module provides:
- Services: Main class for handling document service configurations
- TypeToService: Enum mapping document types to service handlers
"""

from enum import Enum
from http import HTTPStatus
from typing import Any, Union

from app.enums.document.document import ServiceDocumentType
from app.enums.utils import Response

from .balance import BalanceResource
from .company_card import CompanyCardResource
from .financial_statement import FinancialStatementResource
from .legal_procedures_deed import LegalProceduresDeedResource
from .person_report import PersonReportResource
from .report import ReportResource
from .representative_card import RepresentativeCardResource
from .shareholders import ShareholdersResource
from .shares import SharesResource
from .visura import VisuraResource


class Services:
    """
    Class that manages different types of document services and their configurations.

    Each service is defined as a class attribute with a dictionary containing:
        - name: The service name from ServiceDocumentType
        - module: The resource handler for the service
        - document_type: Tuple of valid document types for the service

    Services include:
        - BALANCE: For balance documents
        - COMPANY_CARD: For company card documents
        - FINANCIAL_STATEMENT: For financial statements
        - LEGAL_PROCEDURES_DEED: For legal procedures
        - REPORT: For advisor and expert reports
        - REPRESENTATIVE_CARD: For different types of representative cards
        - VISURA: For visura documents
        - SHAREHOLDERS: For shareholders documents
        - SHARES: For shares documents
        - PERSON_REPORT: For person-related reports
    """

    BALANCE: dict = {
        "name": ServiceDocumentType.BALANCE.name,
        "module": BalanceResource().get,
        "document_type": (ServiceDocumentType.BALANCE.value,),
    }
    COMPANY_CARD: dict = {
        "name": ServiceDocumentType.COMPANY_CARD.name,
        "module": CompanyCardResource().get,
        "document_type": (ServiceDocumentType.COMPANY_CARD.value,),
    }
    FINANCIAL_STATEMENT: dict = {
        "name": ServiceDocumentType.FINANCIAL_STATEMENT.name,
        "module": FinancialStatementResource().get,
        "document_type": (ServiceDocumentType.FINANCIAL_STATEMENT.value,),
    }
    LEGAL_PROCEDURES_DEED: dict = {
        "name": ServiceDocumentType.LEGAL_PROCEDURES_DEED.name,
        "module": LegalProceduresDeedResource().get,
        "document_type": (ServiceDocumentType.LEGAL_PROCEDURES_DEED.value,),
    }
    REPORT: dict = {
        "name": "report",
        "module": ReportResource().get,
        "document_type": (
            ServiceDocumentType.REPORT_ADVISOR.value,
            ServiceDocumentType.REPORT_EXPERT.value,
        ),
    }
    REPRESENTATIVE_CARD: dict = {
        "name": "representative_card",
        "module": RepresentativeCardResource().get,
        "document_type": (
            ServiceDocumentType.REPRESENTATIVE_CARD_COMPLETE.value,
            ServiceDocumentType.REPRESENTATIVE_CARD_CURRENT.value,
            ServiceDocumentType.REPRESENTATIVE_CARD_HISTORY.value,
        ),
    }
    VISURA: dict = {
        "name": ServiceDocumentType.VISURA.name,
        "module": VisuraResource().get,
        "document_type": (
            ServiceDocumentType.VISURA.value,
            ServiceDocumentType.VISURA_HISTORIC.value,
        ),
    }
    SHAREHOLDERS: dict = {
        "name": ServiceDocumentType.SHAREHOLDERS.name,
        "module": ShareholdersResource().get,
        "document_type": (ServiceDocumentType.SHAREHOLDERS.value,),
    }
    SHARES: dict = {
        "name": ServiceDocumentType.SHARES.name,
        "module": SharesResource().get,
        "document_type": (ServiceDocumentType.SHARES.value,),
    }
    PERSON_REPORT: dict = {
        "name": "person_report",
        "module": PersonReportResource().get,
        "document_type": (
            ServiceDocumentType.REPORT_COMPLETE.value,
            ServiceDocumentType.REPORT_STANDARD.value,
        ),
    }

    def __init__(
        self,
        service_type: str,
        document_type: Union[str, None],
        document_id: Union[str, None],
    ) -> None:
        self.service_type = service_type
        self.document_type = document_type
        self.document_id = document_id
        if service_type.upper() not in list(self.__annotations__.keys()):
            self.err = {
                "source": Response.api.value,
                "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                "message": f"'{self.service_type}' is not a valid service member",
            }
        if self.document_type is None:
            if self.service_type.upper() in [
                self.REPORT["name"].upper(),
                self.REPRESENTATIVE_CARD["name"].upper(),
                self.VISURA["name"].upper(),
                self.PERSON_REPORT["name"].upper(),
            ]:
                self.err = {
                    "source": Response.api.value,
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    "message": f"Missing 'document_type' key for service '{self.service_type}'",
                }

        if self.document_id is None:
            if self.service_type.upper() in [
                self.BALANCE["name"].upper(),
                self.FINANCIAL_STATEMENT["name"].upper(),
            ]:
                self.err = {
                    "source": Response.api.value,
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    "message": "'document_id' must be given if service is 'balance' or \
                        'financial_statement'",
                }

    def __call__(self, *args: Any, **kwds: Any) -> Any:
        return getattr(self, self.service_type.upper())["module"](*args, **kwds)
