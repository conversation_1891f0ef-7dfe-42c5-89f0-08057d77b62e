from http import HTTPStatus
from typing import Union

import app.utils as utils
from app.config import Supplier, logger
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.request import Request
from app.resource_access import BaseResource


class CompanyResource(BaseResource):
    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, vat_code, **kwargs) -> tuple:
        """
        Endpoint returning all the information of the Company associated to this particular tax_code

        parameters:
          - name: vat_code
            in : path
            type : string
            required : true
            description : VatCode of a particular company

        responses:
          200 :
            description: Company is present in local db
          201:
            description: Company information is obtained via query towards external data supplier db
          404 :
            description : no Company information available
          422 :
            description : vat_code doesn't represent a valid input
        """
        try:
            logger.log("STARTING PROCESS TO SAVE COMPNY:\n", "i")
            logger.log(
                f"1. SEARCHING FOR VALID COMPANY VALIDATING GIVEN VAT CODE:\n{vat_code}...\n",
                "i",
            )

            if not utils.is_valid_vat_code(vat_code):
                data = {
                    "source": Response.api.value,
                    "message": f"{vat_code} has not a valid format",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }
                return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

            logger.log("2. SEARCHING FOR EXISTING COMPANY BY COMPANY ID...\n", "i")
            company: Company = Company.get_by_vat_code(vat_code=vat_code)

            response = None
            httpstatus = None
            if company is None:
                logger.log("2.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("company")
                a2a = supplier()

                logger.log("2.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.CompanySearch(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    CustomerReferenceText="",
                    SearchData={
                        "VATCode": vat_code,
                        "FlagActiveOnly": 1,
                        "FlagHQOnly": 1,
                        "MaximumHits": 3,
                    },
                )

                if (
                    doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]
                    or doc.TransactionResponse.Result.Code == "CS006"
                ):
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OR 'TransactionResponse.Result.Code' OBJECT ATTRS\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                logger.log("2.3 SAVING NEW COMPANY OBJECT...\n", "i")
                company = Company(
                    ranking=doc.CompanyList.CompanyItem[0].Ranking,
                    name=doc.CompanyList.CompanyItem[0].CompanyName,
                    crif_number=doc.CompanyList.CompanyItem[0].CrifNumber,
                    activity_description=doc.CompanyList.CompanyItem[
                        0
                    ].ActivityDescription,
                    address=doc.CompanyList.CompanyItem[0].Address,
                    town=doc.CompanyList.CompanyItem[0].Town,
                    village=doc.CompanyList.CompanyItem[0].Village,
                    zip_code=doc.CompanyList.CompanyItem[0].Zipcode,
                    province_code=doc.CompanyList.CompanyItem[0].ProvinceCode,
                    ateco_code=doc.CompanyList.CompanyItem[0].AtecoCode,
                    ateco_description=doc.CompanyList.CompanyItem[0].AtecoDescription,
                    province_description=doc.CompanyList.CompanyItem[
                        0
                    ].ProvinceDescription,
                    description=doc.CompanyList.CompanyItem[0].Region,
                    region=doc.CompanyList.CompanyItem[0].Region,
                    tax_code=doc.CompanyList.CompanyItem[0].TAXcode,
                    vat_code=doc.CompanyList.CompanyItem[0].VATCode,
                    cciaa=doc.CompanyList.CompanyItem[0].CCIAA,
                    rea=doc.CompanyList.CompanyItem[0].REA,
                    legal_form_code=doc.CompanyList.CompanyItem[0].LegalFormCode,
                    legal_form_description=doc.CompanyList.CompanyItem[
                        0
                    ].LegalFormDescription,
                    unit_type_code=doc.CompanyList.CompanyItem[0].UnitTypeCode,
                    unit_type_description=doc.CompanyList.CompanyItem[
                        0
                    ].UnitTypeDescription,
                    company_status_code=doc.CompanyList.CompanyItem[
                        0
                    ].CompanyStatusCode,
                    company_status_description=doc.CompanyList.CompanyItem[
                        0
                    ].CompanyStatusDescription,
                    activity_status_code=doc.CompanyList.CompanyItem[
                        0
                    ].ActivityStatusCode,
                    activity_status_code_description=doc.CompanyList.CompanyItem[
                        0
                    ].ActivityStatusCodeDescription,
                    flag_out_of_business=doc.CompanyList.CompanyItem[
                        0
                    ].FlagOutOfBusiness,
                    flag_news_available=doc.CompanyList.CompanyItem[
                        0
                    ].FlagNewsAvailable,
                    flag_payment_info_available=doc.CompanyList.CompanyItem[
                        0
                    ].FlagPaymentInfoAvailable,
                    last_balance_date=doc.CompanyList.CompanyItem[0].LastBalanceDate,
                    flag_belong_to_a_group=doc.CompanyList.CompanyItem[
                        0
                    ].FlagBelongToAGroup,
                    flag_pa=doc.CompanyList.CompanyItem[0].FlagPa,
                    website=doc.CompanyList.CompanyItem[0].WebSite,
                )
                company.save()

                logger.log("2.4 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    purchase=True,
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_code=doc.TransactionResponse.Result.Code,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    user_id=self.guest.user_id,
                    company_id=str(company.id),
                )
                request.save()

                response = {
                    "activity_description": company.activity_description,
                    "address": company.address,
                    "ateco_code": company.ateco_code,
                    "crif_number": company.crif_number,
                    "id": str(company.id),
                    "last_balance_date": (
                        company.last_balance_date.strftime("%d-%m-%Y")
                        if company.last_balance_date
                        else None
                    ),
                    "legal_form_description": company.legal_form_description,
                    "name": company.name,
                    "province_description": company.province_description,
                    "ranking": (
                        float(company.ranking) if company.ranking is not None else None
                    ),
                    "rea": company.rea,
                    "town": company.town,
                    "vat_code": company.vat_code,
                    "website": company.website,
                    "zip_code": company.zip_code,
                }
                httpstatus = HTTPStatus.CREATED

            if response is None:
                logger.log("2.1 COMPANY ALREADY EXISTS: BUILDING RESPONSE...\n", "i")
                response = {
                    "activity_description": company.activity_description,
                    "address": company.address,
                    "ateco_code": company.ateco_code,
                    "crif_number": company.crif_number,
                    "id": str(company.id),
                    "last_balance_date": (
                        company.last_balance_date.strftime("%d-%m-%Y")
                        if company.last_balance_date
                        else None
                    ),
                    "legal_form_description": company.legal_form_description,
                    "name": company.name,
                    "province_description": company.province_description,
                    "ranking": float(company.ranking),
                    "rea": company.rea,
                    "town": company.town,
                    "vat_code": company.vat_code,
                }
                httpstatus = HTTPStatus.OK

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
