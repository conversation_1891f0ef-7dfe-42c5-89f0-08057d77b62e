from http import HTTPStatus

from flask_restful import Resource


class NotFoundResource(Resource):
    def get(self) -> tuple:
        """
        Endpoint returning a 404 error response.

        :return: A tuple containing a message indicating that there is no resource available at this endpoint and a 404 status code.
        :rtype: tuple

        :responses:
            404:
                description: There is no resource available at this endpoint.
        """
        data = "There is no resource available at this endpoint"

        return (data, HTTPStatus.NOT_FOUND)
