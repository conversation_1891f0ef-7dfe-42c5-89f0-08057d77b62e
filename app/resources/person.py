from http import HTTPStatus
from typing import Union

from app.config import Supplier, logger
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.person import Person
from app.models.request import Request
from app.resource_access import BaseResource
from app.utils import is_valid_tax_code


class PersonResource(BaseResource):
    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, tax_code, **kwargs) -> tuple:
        """
        Endpoint for searching a person using as input his/her tax code

        parameters:
          - name: tax_code
            in : path
            type : string
            required : true

        responses:
          200 :
            description : person data are present in the database
          201 :
            description : person data is obtained via external data supplier db
          422 :
            description : input is not valid
        """
        try:
            logger.log("STARTING PROCESS TO SAVE PERSON:\n", "i")
            logger.log(
                f"1. SEARCHING FOR VALID PERSON VALIDATING GIVEN VAT CODE:\n{tax_code}...\n",
                "i",
            )

            if not is_valid_tax_code(tax_code):
                data = {
                    "source": Response.api.value,
                    "message": f"{tax_code} is not a valid tax code",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }
                return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

            logger.log("2. SEARCHING FOR EXISTING PERSON BY PERSON ID...\n", "i")
            person: Person = Person.get_by_tax_code(tax_code=tax_code)

            response = None
            httpstatus = None
            if person is None:
                logger.log("2.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("person")
                a2a = supplier()

                logger.log("2.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.PersonSearch(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    SearchData={"TAXCode": tax_code},
                )

                if (
                    doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]
                    or doc.PersonList is None
                ):
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OR 'PersonList' OBJECT ATTRS\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                logger.log("2.3 SAVING NEW PERSON OBJECT...\n", "i")
                person = Person(
                    ranking=float(doc.PersonList.PersonItem[0].Ranking),
                    surname=doc.PersonList.PersonItem[0].Surname,
                    name=doc.PersonList.PersonItem[0].Name,
                    tax_code=doc.PersonList.PersonItem[0].TAXCode,
                    birth_date=doc.PersonList.PersonItem[0].BirthDate,
                    birth_town=doc.PersonList.PersonItem[0].BirthTown,
                    birth_province=doc.PersonList.PersonItem[0].BirthProvince,
                    birth_province_description=doc.PersonList.PersonItem[
                        0
                    ].BirthProvinceDescription,
                    gender=doc.PersonList.PersonItem[0].Gender,
                    address=doc.PersonList.PersonItem[0].Address,
                    town=doc.PersonList.PersonItem[0].Town,
                    village=doc.PersonList.PersonItem[0].Village,
                    province=doc.PersonList.PersonItem[0].Province,
                    zip=doc.PersonList.PersonItem[0].Zip,
                    country=doc.PersonList.PersonItem[0].Country,
                    has_roles=doc.PersonList.PersonItem[0].HasRoles,
                    is_sole_trader=doc.PersonList.PersonItem[0].IsSoletrader,
                    is_shareholder=doc.PersonList.PersonItem[0].IsShareholder,
                )
                person.save()

                logger.log("2.4 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_code=doc.TransactionResponse.Result.Code,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    user_id=self.guest.user_id,
                    person_id=person.id,
                    purchase=False,
                )
                request.save()

                response = {
                    "id": str(person.id),
                    "name": person.name,
                    "surname": person.surname,
                    "tax_code": person.tax_code,
                    "ranking": person.ranking,
                }
                httpstatus = HTTPStatus.CREATED

            if response is None:
                logger.log("2.1 PERSON ALREADY EXISTS: BUILDING RESPONSE...\n", "i")
                response = {
                    "id": str(person.id),
                    "name": person.name,
                    "surname": person.surname,
                    "tax_code": person.tax_code,
                    "ranking": person.ranking,
                }

            httpstatus = HTTPStatus.OK if httpstatus is None else httpstatus

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
