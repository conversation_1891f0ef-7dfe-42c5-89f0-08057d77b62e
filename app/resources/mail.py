"""
This module defines the `MailResource` class, which handles sending HTML-formatted emails based on
predefined templates.

The `MailResource` class provides a RESTful API resource for sending emails
by specifying a recipient, email type, and optional variables. It utilizes the
`MailConfig` class and `MailTemplate` enum to generate the appropriate
email content, and the Flask-Mail extension to handle the actual sending.
"""

import os
import re
import socket
from http import HTTPStatus
from smtplib import SMTPException

from flask import render_template, request
from flask_mail import Message
from flask_restful import Resource

from app.config import MailConfig, logger
from app.enums.mail.mail import MailTemplate
from app.extensions import email


class MailResource(Resource):
    """
    Resource for sending HTML-formatted emails based on predefined templates and types.
    """

    def post(self):
        """
        Handles the POST request to send an email using provided parameters.

        The POST method will now delegate the email sending responsibility to the `send` method.

        Returns:
            On success: {"message": "Email sent successfully"}
            On failure: Error details with HTTPStatus.INTERNAL_SERVER_ERROR
        """

        try:
            data = request.get_json()
            recipient = data.get("recipient")
            message = data.get("message")
            email_type = data.get("type").upper()

            if (
                not recipient
                or not email_type
                or not message
                or not isinstance(message, dict)
                or not self.is_valid_email(recipient)
            ):
                logger.log(
                    f"Bad request: Missing required fields or invalid email format. Recipient: \
                        {recipient}, Email Type: {email_type}, Message Provided: {bool(message)}",
                    "w",
                )
                return {
                    "source": "Client",
                    "error": HTTPStatus.BAD_REQUEST,
                    "message": "Email type, message and recipient must be provided. Recipient shoul"
                    "d always be a valid email address.",
                }, HTTPStatus.BAD_REQUEST

            try:
                email_type_enum = MailTemplate[email_type]
            except KeyError:
                logger.log(f"Invalid email type provided: {email_type}", "w")
                return {
                    "source": "Client",
                    "error": HTTPStatus.BAD_REQUEST,
                    "message": "Invalid email type provided.",
                }, HTTPStatus.BAD_REQUEST

            required_variables = email_type_enum.get_variables()
            missing_variables = []
            for var in required_variables:
                keys = var.split(".")
                temp_message = message
                for key in keys:
                    if key in temp_message:
                        temp_message = temp_message[key]
                    else:
                        missing_variables.append(var)
                        break

            if missing_variables:
                logger.log(
                    f"Missing variables in the message: {', '.join(missing_variables)}. Email Type:\
                        {email_type}, Recipient: {recipient}"
                )
                return (
                    {
                        "source": "Client",
                        "error": HTTPStatus.BAD_REQUEST,
                        "message": f"Missing required variables in the message \
                        : {', '.join(missing_variables)}",
                    },
                    HTTPStatus.BAD_REQUEST,
                )

            return self.send(
                recipient=recipient,
                email_type=email_type_enum,
                message=message,
            )

        except (SMTPException, socket.error) as e:
            logger.log(
                f"Unexpected error while processing the email request: {str(e)}", "c"
            )
            return {
                "source": "Server",
                "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                "message": "An unexpected server error occurred. Please try again later.",
                "details": str(e),
            }, HTTPStatus.INTERNAL_SERVER_ERROR

    @staticmethod
    def save_email_as_html(recipient, subject, body):
        """
        Saves the email content as an HTML file in case the SMTP server is not reachable.

        Args:
            recipient (str): Email address to send to.
            subject (str): The subject of the email.
            body (str): The HTML message body content.

        Returns:
            None
        """
        file_path = os.path.join(
            f"{os.getcwd()}/temporary/" f"test_email_{recipient}.html"
        )

        email_html = render_template("email.html", subject=subject, body=body)

        with open(file_path, "w", encoding="utf-8") as file:
            file.write(email_html)

        logger.log(
            f"Email content saved to {file_path} for recipient {recipient}.", "i"
        )

    @staticmethod
    def send(recipient, email_type: MailTemplate, message: dict):
        """
        Sends an HTML-formatted email using the specified email type and variables.

        Args:
            recipient (str): Email address to send to.
            email_type (MailTemplate): The type of email to send.
            variables (dict): A dictionary containing variables for the message content.

        Returns:
            Tuple[dict, HTTPStatus]: A response message and the corresponding HTTP status code.
        """
        try:
            subject, body = MailConfig.get_email_content(email_type, message)
            html_content = render_template("email.html", subject=subject, body=body)
            msg = Message(
                subject=subject,
                recipients=[recipient],
                html=html_content,
            )

            email.send(msg)
            logger.log(
                f"Email sent successfully to {recipient}. Email Type: {email_type}", "i"
            )
            return {"message": "Email sent successfully"}, HTTPStatus.OK

        except (SMTPException, socket.error) as e:
            logger.log(f"Failed to send email to {recipient}. Error: {str(e)}", "c")
            if os.getenv("ENVIRONMENT") == "development":
                MailResource.save_email_as_html(recipient, subject, body)
            return {
                "source": "Server",
                "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                "message": "Please contact the administrator",
                "details": str(e),
            }, HTTPStatus.INTERNAL_SERVER_ERROR

    @staticmethod
    def is_valid_email(email_address: str) -> bool:
        """
        Validates the format of an email address using a regular expression.

        This method checks if the provided email address matches a basic pattern for valid emails:
        - Contains a single "@" character.
        - Has a valid domain name after the "@".
        - Contains a dot in the domain portion.

        Args:
            email_address (str): The email address to validate.

        Returns:
            bool: True if the email address format is valid, False otherwise.
        """
        return re.match(r"[^@]+@[^@]+\.[^@]+", email_address)
