"""
Module for providing statistical information about the application's data.

This module defines a RESTful resource that provides access to statistical information
about various entities in the system, including requests, documents, and users.
It allows clients to retrieve all records for a specific entity type.

Classes:
    StatsResource: A Flask-RESTful resource that provides statistical data.
"""

from http import HTTPStatus

from flask_restful import Resource

from app.extensions import session
from app.models.document import Document
from app.models.request import Request
from app.models.user import User


class StatsResource(Resource):
    """
    A RESTful resource that provides statistical information about system entities.

    This resource allows API consumers to retrieve all records for a specific entity type,
    such as requests, documents, or users. It's primarily intended for administrative
    and reporting purposes.

    Endpoints:
        GET /stats/<entity>: Retrieves all records for the specified entity type.
    """

    def get(self, entity):
        """
        Retrieve all records for a specific entity type.

        This method returns all records for the requested
        entity type (requests, documents, or users).
        If an invalid entity type is provided, it returns a 404 error.

        Args:
            entity (str): The type of entity to retrieve statistics for.
                          Valid values are "requests", "documents", and "users".

        Returns:
            tuple: A tuple containing:
                - The response data (all records for the requested entity)
                - HTTP status code
                - Optional header directive ("close_session")

        Response Codes:
            200 OK: Successfully retrieved the requested data
            404 Not Found: The specified entity type is invalid

        Example:
            GET /stats/users
            Response: ({"user_data": [...], "count": 42}, 200, "close_session")
        """
        if entity == "requests":
            all_requests = Request.query.all()
            return [r.as_dict() for r in all_requests], HTTPStatus.OK

        elif entity == "documents":
            all_documents = session.query(Document).all()
            return [d.as_dict() for d in all_documents], HTTPStatus.OK

        elif entity == "users":
            all_users = User.query.all()
            return (
                [u.as_dict() for u in all_users],
                HTTPStatus.OK,
            )

        else:
            return {"error": "Invalid entity"}, HTTPStatus.NOT_FOUND
