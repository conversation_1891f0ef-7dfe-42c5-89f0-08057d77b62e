"""
This module provides user access control, handling authentication and user session management.

Classes:
    UserAccess:
        Manages user retrieval and creation, ensuring proper authentication and authorization.
        Implements the `get` method to return existing users or create new ones based
        on the provided headers.
"""

from http import HTTPStatus
from typing import Union

from app.auth.auth_config import authconfig
from app.extensions import manage_transaction
from app.resource_access import BaseResource


class UserAccess(BaseResource):
    """
    Manages user access and session handling within the application.

    This class interacts with the authentication configuration to ensure users are properly
    authenticated. It provides a `get` method to either retrieve an existing user or create a new
    one based on the provided headers.

    Attributes:
        check_on_ (Union[tuple, None]): Optional tuple for checking specific conditions.
        doc_type_ (Union[tuple, None]): Optional tuple for specifying document types.
        access_headers (tuple): Headers required for user access, 'Id' and 'Authorization'.

    Methods:
        get(**kwargs) -> tuple:
            Retrieves or creates a user based on the provided user ID header, ensuring proper
            session management.
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None
    access_headers: tuple = (
        "Id",
        "Authorization",
    )

    @manage_transaction
    def get(self, **kwargs) -> tuple:
        """
        Endpoint that returns or creates user by given user_id (as header)

        parameters:
          None

        responses:
          200:
            description : existing user
          201:
            description : new user
        """
        try:
            if authconfig.is_active:
                return (self.guest.as_dict(), HTTPStatus.OK, "close_session")
            return (
                {
                    "source": "API root auth",
                    "error": HTTPStatus.UNAUTHORIZED,
                    "message": "Login required",
                },
                HTTPStatus.UNAUTHORIZED,
                "close_session",
            )
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
