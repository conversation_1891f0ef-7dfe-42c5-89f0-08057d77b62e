from datetime import datetime
from http import HTTPStatus
from typing import Union
from uuid import UUID

import xmltodict

from app.config import Supplier, logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.request import Request
from app.resource_access import BaseResource
from app.storage.azstorage import AZStorage
from app.utils import build_headers_params, lock_action, utcnow
from app.validate import validate_company, validate_document


class BalanceListResource(BaseResource):
    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, company_id, **kwargs) -> tuple:
        """
        Endpoint returning all the balances in the db associated to this particular vat_code

        parameters:
          - name: company_id
            in : path
            type : UUID
            required : true

        responses:
          200 :
            description: a list of balances was available in the database
          201 :
            description : after having queried external data supplier db a list of balances was created
          404 :
            description : no balances are available on external data supplier db
          422 :
            description : vat_code doesn't represent a valid vat_code
        """
        try:
            logger.log("STARTING PROCESS TO SAVE BALANCES LIST:\n", "i")
            logger.log("1. VALIDATING COMPANY BY COMPANY ID...\n", "i")

            company: Company = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log(f"1.1 COMPANY FOUND:\n{company}\n", "i")

            balance_list = Document.get_by_company_code_and_document_types(
                document_types=[ServiceDocumentType.BALANCE.value],
                company_code=company.vat_code,
            )

            logger.log("2. CHECKING IF DB BALANCES ALREADY ADDED INTO THE DB...\n", "i")

            response = list()
            if len(balance_list) == 0:
                logger.log("2.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("balance")
                a2a = supplier()

                logger.log("2.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.GetOpticalBalancesSearchByTAXCode(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    TaxCode=company.tax_code,
                )

                # Controllo che la risorsa sia stata correttamente estratta
                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if doc.Document.__contains__('CodErrore="727"'):
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'Document' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": f"{company.vat_code} has not a valid format",
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")
                response_dict = xmltodict.parse(doc.Document)

                logger.log("2.3 CHECKING FOR OBJECT ATTRS...\n", "i")

                if (
                    "Anagrafica"
                    in response_dict["BondRA_Output"]["DocumentOutput"]["BondRAOutput"]
                ):
                    balances_list = list()
                    iterable_ = response_dict["BondRA_Output"]["DocumentOutput"][
                        "BondRAOutput"
                    ]["Anagrafica"]
                    if isinstance(iterable_, list):
                        for idx, el in enumerate(iterable_):
                            if "Bilancio" in el:
                                balances_list.extend(iterable_[idx]["Bilancio"])

                    if isinstance(iterable_, dict):
                        if "Bilancio" in iterable_:
                            balances_list = (
                                iterable_["Bilancio"]
                                if isinstance(iterable_["Bilancio"], list)
                                else [iterable_["Bilancio"]]
                            )

                    logger.log(
                        "2.4 CREATING 'Document' INSTANCE FOR EACH BALANCE RETURNED...\n",
                        "i",
                    )
                    for balance in balances_list:
                        balance = Document(
                            company_code=company.vat_code,
                            code_atto=balance["@CodiceAtto"],
                            data_chiusura_esercizio=balance["@DataChiusuraEsercizio"],
                            num_pag_bilancio=balance["@NumPagineBilancio"],
                            ref_id=balance["@ChiaveDocFisico"],
                            type=ServiceDocumentType.BALANCE.value,
                            year=datetime.strptime(
                                balance["@DataChiusuraEsercizio"], "%Y%m%d"
                            ).year,
                        )

                        logger.log(f"2.4.1 SAVING BALANCE:\n{balance}\n", "i")
                        balance.save()
                        balance_dict = {
                            "code_atto": balance.code_atto,
                            "file_url": True if balance.file_url else False,
                            "type": balance.type,
                            "id": str(balance.id),
                            "vat_code": company.vat_code,
                            "year": balance.year,
                        }
                        response.append(balance_dict)
                    httpstatus = HTTPStatus.CREATED

                    logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
                    return (response, HTTPStatus.OK, "close_session")

            logger.log(
                "3. BALANCES ALREADY ADDED. CREATING RESPONSE WITH EXISTING DOCS...\n",
                "i",
            )
            if not len(response):
                httpstatus = HTTPStatus.OK
                for balance in balance_list:
                    balance_dict = {
                        "code_atto": balance.code_atto,
                        "file_url": True if balance.file_url else False,
                        "type": balance.type,
                        "id": str(balance.id),
                        "vat_code": balance.company_code,
                        "year": datetime.strptime(balance.year, "%Y").year,
                    }
                    response.append(balance_dict)

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class BalanceResource(BaseResource):
    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = (DocsPricesEnum.BALANCE.name,)
    access_headers: tuple = ("coc",)

    @manage_transaction
    def get(
        self, company_id: UUID, document_id: UUID, by_approval: bool = False, **kwargs
    ) -> tuple:
        """
        Endpoint returning a particular balance thanks to the vat code and the doc key.

        parameters:
          - name: company_id
            in : path
            type : UUID
            required : true
          - name: document_id
            in : path
            type : UUID
            required :true

        responses:
          200 :
            description: returns the the pdf document as a base64 encoded string
          201 :
            description :
          404 :
            description : no balances are available on external data supplier db
          422 :
            description : vat_code doesn't represent a valid vat_code
        """
        try:
            logger.log("STARTING PROCESS TO SAVE BALANCE DOC:\n", "i")
            logger.log(f"1. INTERPRETING ACTIONS FROM kwargs:\n{kwargs}...\n", "i")

            action = lock_action(kwargs)
            rebuy = kwargs.get("rebuy")
            if by_approval or rebuy:
                setattr(self, "guest", kwargs["self.guest"])

            logger.log("2. VALIDATING COMPANY BY COMPANY ID...\n", "i")
            company: Union[Company, tuple] = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log("3. LOOKING FOR EXISITNG DOCUMENT...\n", "i")
            balance: Union[Document, tuple] = validate_document(document_id)
            if isinstance(balance, tuple):
                logger.log("DOCUMENT NOT FOUND INTO THE DB\n", "w")
                return balance

            if company.vat_code != balance.company_code:
                logger.log("WRONG VAT CODE IDENTIFIER\n", "w")
                data = {
                    "source": Response.api.value,
                    "message": f"Document with id '{document_id}' not related to the Company with id '{company_id}'",
                    "error": HTTPStatus.NOT_FOUND,
                }
                return (data, HTTPStatus.NOT_FOUND, "close_session")

            if balance.type != ServiceDocumentType.BALANCE.value:
                logger.log("INVALID DOCUMENT TYPE VALUE\n", "i")
                data = {
                    "source": Response.api.value,
                    "message": f"Document with id '{document_id}' it's not of type 'balance'",
                    "error": HTTPStatus.NOT_FOUND,
                }
                return (data, HTTPStatus.NOT_FOUND, "close_session")

            logger.log("4. BUILDING HEADERS PARAMS...\n", "i")
            vars_ = build_headers_params(self, kwargs, self.access_headers)
            coc_ = vars_["coc"]
            expire = "close_session" if not by_approval else None
            now = utcnow()

            response = None
            httpstatus = None
            remove_credits = False
            residual_credits = dict()
            azstorage: AZStorage = AZStorage()

            logger.log("5. CHECKING IF DB DOCUMENT ALREADY ADDED INTO THE DB...\n", "i")
            if balance.file_url is None or rebuy:
                logger.log("5.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("balance")
                a2a = supplier()

                logger.log("5.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.GetOpticalBalancesDetailsDocument(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    TaxCode=balance.company_code,
                    KeyDoc=balance.ref_id,
                    Name="Test",
                    Prospects="",
                )

                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity'"
                        " OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if rebuy:
                    logger.log("5.3 STARTIG REBUY PROCESS...\n", "i")
                    return {
                        "doc": doc.BusinessReportAttachments.ProductAttachment[
                            0
                        ].Content,
                        "azstorage": azstorage,
                    }

                logger.log("5.4 UPLOADING ON AZURE CONTAINER...\n", "i")
                balance.updated_at = doc.TransactionResponse.Details.ResponseTimestamp
                azurl = azstorage.upload(
                    balance,
                    doc.BusinessReportAttachments.ProductAttachment[0].Content,
                    ServiceDocumentType.BALANCE.value,
                )
                balance.file_url = azurl

                logger.log("5.5 UPDATING RESULT...\n", "i")
                balance.update()

                logger.log("5.6 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    purchase=True,
                    document_id=balance.id,
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_code=doc.TransactionResponse.Result.Code,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    user_id=self.guest.user_id,
                    bought_by=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.6.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = (
                    {
                        "vat_code": balance.company_code,
                        "file_url": balance.file_url,
                        "id": str(balance.id),
                        "type": balance.type,
                    }
                    if not by_approval
                    else request.as_dict()
                )
                httpstatus = HTTPStatus.CREATED
                remove_credits = True

            if response is None:
                logger.log(
                    "5.1 DOCUMENT ALREADY EXISTS: CREATING NEW REQUEST OBJECT TO SAVE...\n",
                    "i",
                )
                logger.log("5.2 CREATING REQUEST OBJECT...\n", "i")

                request = Request(
                    request_timestamp=now,
                    response_timestamp=utcnow(),
                    purchase=False,
                    document_id=balance.id,
                    user_id=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.2.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = {
                    "vat_code": balance.company_code,
                    "file_url": azstorage.download(balance),
                    "id": str(balance.id),
                    "type": balance.type,
                }

                residual_credits = {
                    "residual_credits": float(round(self.guest.residual_credits, 2))
                }
                httpstatus = HTTPStatus.OK

            # Remove credits only if API response is success
            if remove_credits:
                logger.log("6 REMOVING CREDITS IF NEEDED...\n", "i")
                residual_credits = {
                    "residual_credits": self.guest.reduce_resources(
                        DocsPricesEnum.BALANCE.value, "residual_credits"
                    )
                }
            response = {**response, **residual_credits}

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus, expire)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
