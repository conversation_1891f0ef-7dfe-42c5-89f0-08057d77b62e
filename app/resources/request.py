"""
This module handles the various operations related to managing purchase requests,
including creating, updating, approving, rejecting, retrieving, and deleting requests.
It defines the request lifecycle from creation to potential approval or rejection,
and manages the associated resources, such as user credits and email notifications.
"""

import json
import socket
from http import HTTPStatus
from smtplib import SMTPException
from typing import Union
from uuid import UUID

from sqlalchemy import Column

from app.config import logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.entities import Entities
from app.enums.mail.mail import MailTemplate
from app.enums.request.action import ActionEnum
from app.enums.request.status import StatusEnum
from app.enums.user.resources import UserResources
from app.enums.utils import Response
from app.extensions import higher_level_role_required, manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.person import Person
from app.models.request import Request
from app.models.user import User
from app.resource_access import BaseResource
from app.resources.mail import MailResource as mail

from . import Services


class RequestPut(BaseResource):
    """
    Handles the retrieval of purchase requests associated with an approver user.
    The requests can be filtered based on criteria such as request ID or year.
    The retrieved requests include details about the associated entity (company or person),
    document type, and other relevant information.
    """

    check_on_: Union[tuple, None] = ("requests",)
    doc_type_: Union[tuple, None] = None
    access_body: bool = True

    @manage_transaction
    def put(self, entity_type: Entities, entity_id: UUID, **kwargs) -> tuple:
        """
        Add a purchase request. Purchasing parameters are given by a body.
        Body is different from balance and financial_statement cases to others. Exmample:

            - balance and financial_statement

                {
                    "service_request_info": {
                        "service_type": {balance OR financial_statement},
                        "method_params": {
                            "company_id": {company_id},
                            "document_id": {document_id},
                            "document_type": {document_type}
                            }
                        },
                    "coc": "{coc}",
                    "action": "rebuy" --> Optional: only if is a rebuy process
                }

                'id' param is the only id needed.

            - Report (like other cases):

                {
                    "service_request_info": {
                        "service_type": {service type (example: "report")},
                        "method_params": {
                                "document_type": {document type (example: "RptAdvisor")},
                                {"company_id" OR "person_id"}: {entity_id}
                            }
                        },
                    "coc": "{coc}",
                    "action": "rebuy" --> Optional: only if is a rebuy process
                }

        parameters:
          None

        responses:
          201:
            description : request created
          422:
            description : input not valid
          404:
            description : no present in the database
          409:
            description : request already exists
        """
        try:
            logger.log("Saving new purchase request:\n", "i")
            if self.guest.residual_requests == 0:
                return {
                    "source": Response.api.value,
                    "message": "Not enough residual requests to process",
                    "error": HTTPStatus.BAD_REQUEST,
                }

            if entity_type not in list(Entities.__members__):
                return {
                    "source": Response.api.value,
                    "message": f"Value {entity_type} is not a valid Entties member",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }

            logger.log(
                "1. Validating entity (company or person) by entity id...\n", "i"
            )
            func = Entities(entity_type).func()
            entity = func(entity_id)
            if isinstance(entity, tuple):
                logger.log("Entity not found into the db\n", "w")
                return entity

            logger.log("2. Building body and service to call...\n", "i")
            body = {
                "user_id": self.guest.user_id,
                **json.loads(self.request.data),
                Entities(entity_type).params(): str(entity.id),
            }

            # Check service type and document type
            service = Services(
                body["service_request_info"]["service_type"],
                body["service_request_info"]["method_params"].get("document_type"),
                body["service_request_info"]["method_params"].get("document_id"),
            )

            logger.log(f"Body:\n{body}\Service:\n{service}\n", "i")
            if hasattr(service, "err"):
                logger.log(
                    "Related service not found. Probably wrong payload\n",
                    "c",
                )
                return (service.err, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

            # Check if same request already exists in DB
            err = (
                {
                    "source": Response.api.value,
                    "message": "A purchase request for this document is "
                    "already under approving review",
                    "error": HTTPStatus.CONFLICT,
                },
                HTTPStatus.CONFLICT,
                "close_session",
            )

            logger.log("3. Checking if the request is already been made...\n", "i")
            existing_request = Request.query_by_criteria(
                *[
                    Column("requested_at") == body["requested_at"],
                    Column("status") == StatusEnum.PENDING.value,
                    Column("user_id") == self.guest.user_id,
                ]
            )
            for req in existing_request:
                if req.service_request_info is not None:
                    if Request.request_already_exists(
                        body["service_request_info"], req.service_request_info
                    ):
                        return err

            # Check if the approver exist and has the necessary credits
            approver = User.get_by_id(id=body["requested_at"])
            if approver is None:
                logger.log(
                    f"Approver with ID {body['requested_at']} not found in database",
                    "c",
                )
                return (
                    {
                        "source": Response.api.value,
                        "message": "The specified approver does not exist",
                        "error": HTTPStatus.NOT_FOUND,
                    },
                    HTTPStatus.NOT_FOUND,
                    "close_session",
                )
            price = getattr(
                DocsPricesEnum,
                ServiceDocumentType(
                    body["service_request_info"]["method_params"]["document_type"]
                ).name.upper(),
            ).value
            if price > approver.residual_credits:
                return (
                    {
                        "source": Response.api.value,
                        "message": "The approver doesn't have enough credits"
                        " to satisfy the request.",
                        "error": HTTPStatus.NOT_ACCEPTABLE,
                    },
                    HTTPStatus.NOT_ACCEPTABLE,
                    "close_session",
                )

            logger.log("4. Validating body base params...\n", "i")
            if body.get("action") is None or body.get("action") not in [
                ActionEnum.BUY.value,
                ActionEnum.REBUY.value,
            ]:
                body["action"] = ActionEnum.BUY.value
            if body["action"] == ActionEnum.REBUY.value:
                if "document_id" not in body["service_request_info"]["method_params"]:
                    return (
                        {
                            "source": Response.api.value,
                            "message": "A rebuy request must have a 'document_id' relation.",
                            "error": HTTPStatus.NOT_ACCEPTABLE,
                        },
                        HTTPStatus.NOT_ACCEPTABLE,
                        "close_session",
                    )

            # Put new request
            logger.log("5. Creating and adding new request...\n", "i")
            request = Request(**body)
            return_value: Request = request.ask_request()
            return_value = return_value.as_dict()

            # Reduce resources
            logger.log("6. Reduce requests of the user...\n", "i")
            residual_requests = {
                "residual_requests": self.guest.reduce_resources(
                    1,
                    "residual_requests",
                )
            }

            # Send emails to both the aprover and the user
            # If the approver never used the app is going to receive a different email
            try:
                logger.log("8. Sending email to the user...\n", "i")
                mail.send(
                    recipient=self.guest.email,
                    email_type=MailTemplate.NEW_REQUEST,
                    message={
                        "approver_name": approver.name,
                        "coc": body["coc"],
                        "company": entity.name,
                        "document_type": body["service_request_info"]["method_params"][
                            "document_type"
                        ],
                        "price": price,
                        "tax_code": entity.tax_code,
                        "user_name": self.guest.name,
                    },
                )

                logger.log("7. Sending email to the approver...\n", "i")
                template = (
                    MailTemplate.FIRST_REQUEST_RECEIVED
                    if approver.last_access is None
                    else MailTemplate.NEW_REQUEST_RECEIVED
                )
                mail.send(
                    recipient=approver.email,
                    email_type=template,
                    message={
                        "approver_name": approver.name,
                        "coc": body["coc"],
                        "company": entity.name,
                        "document_type": body["service_request_info"]["method_params"][
                            "document_type"
                        ],
                        "price": price,
                        "tax_code": entity.tax_code,
                        "user_name": self.guest.name,
                        "user_surname": self.guest.surname or "surname",
                        "document_company": entity.name,
                        "document_selected": body["service_request_info"][
                            "method_params"
                        ]["document_type"],
                    },
                )
            except (SMTPException, socket.error) as e:
                logger.log(
                    f"Failed to send email to { self.guest.email or approver.email } for the request {body['coc']}, \
                        {entity.name}, {body['service_request_info']['method_params']['document_type']} \
                            : {e}",
                    "c",
                )

            logger.log(
                f"Returning response:\n{return_value}\n{residual_requests}\n", "i"
            )
            return (
                {**return_value, **residual_requests},
                HTTPStatus.CREATED,
                "close_session",
            )
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class RequestPost(BaseResource):
    """
    Handles the deletion of a purchase request by a consumer user.
    This includes removing the request from the database.
    The method ensures the transaction is managed properly and returns
    appropriate status responses based on the success or failure of the deletion process.
    """

    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = None

    @higher_level_role_required
    @manage_transaction
    def post(self, request_id: UUID, request__=dict(), **kwargs) -> tuple:
        """
        Endpoint that approves or denies a purchase request.
        Body param:

            {
                "approve": True/False,
                "document_type": DOC TYPE
            }

            - if True, the related service is called to purchase the specific
            document of a request bound to 'request_id'. Once bought the document,
            the request is goint to be setted as 'approved' from 'pending'.
            If Fasle, the request is going to be setted as 'rejected' and no service is
            called.

        parameters:
          - request_id : id of the request

        responses:
          201:
            description : doc bought
          422:
            description : input not valid
          404:
            description : no present in the database
        """
        try:
            logger.log("Starting process to accept/reject purchase request:\n", "i")
            logger.log("1. Getting body and checking for asked request...\n", "i")
            body = json.loads(self.request.data)

            # Preconditions of existing request
            user_request = Request.query_by_criteria(*[Column("id") == request_id])
            if not user_request:
                return (
                    {
                        "source": Response.api.value,
                        "message": "There are not purchase request under"
                        " approving review for this user",
                        "error": HTTPStatus.NOT_FOUND,
                    },
                    HTTPStatus.NOT_FOUND,
                    "close_session",
                )
            user_request: Request = user_request[0]
            requesting_user = User.get_by_user_id(user_request.user_id)

            # Reject purchase request
            if not body["service_request_info"]["approve"]:
                logger.log("2. Rejecting request...\n", "i")
                response = user_request.reject(
                    user_request.service_request_info.get("document_id")
                )
                logger.log("3. Request rejected\n", "i")

                try:
                    mail.send(
                        recipient=requesting_user.email,
                        email_type=MailTemplate.REQUEST_REJECTED,
                        message={
                            "coc": user_request.coc,
                            "user_name": requesting_user.name,
                        },
                    )
                    logger.log("4. Sent the user the rejected request email\n", "i")
                except (SMTPException, socket.error) as e:
                    logger.log(
                        f"Failed to send email to the user for the rejected request: {e}",
                        "c",
                    )
                return (response, HTTPStatus.OK, "close_session")

            # Handle rebuy request
            if user_request.action == ActionEnum.REBUY.value:
                logger.log("1.1 Processing rebuy by purchase request...\n", "i")
                from .rebuy_document import Rebuy

                if user_request.status == StatusEnum.ACCEPTED.value:
                    return (
                        {
                            "source": Response.api.value,
                            "message": "Rebuy request already been processed",
                            "error": HTTPStatus.CONFLICT,
                        },
                        HTTPStatus.CONFLICT,
                        "close_session",
                    )

                request = user_request.as_dict()
                request.pop("document_id", None)
                _kwargs = {
                    **kwargs,
                    **request,
                    "self.guest": self.guest,
                    "by_approval": True,
                    "request_obj": user_request,
                    "Id": self.guest.user_id,
                }

                logger.log(
                    f"1.2 Built params to call the related service:\n{_kwargs}\n \
                        calling the service...\n",
                    "i",
                )
                rebuy = Rebuy().patch(
                    user_request.service_request_info["method_params"].get(
                        "document_id"
                    ),
                    **_kwargs,
                )
                if isinstance(rebuy[1], HTTPStatus):
                    logger.log(
                        f"Something went wrong in the process of rebuy under approval:\n{rebuy}\n",
                        "c",
                    )
                    return (*rebuy, "close_session")
                return rebuy

            # Approve purchase request
            service = Services(
                user_request.service_request_info["service_type"],
                user_request.service_request_info["method_params"].get("document_type"),
                user_request.service_request_info["method_params"].get("document_id"),
            )

            logger.log(f"2. Service built:\n{service}\nnow calling it...\n", "i")
            service_call = service(
                **{
                    **user_request.service_request_info["method_params"],
                    "by_approval": True,
                    "coc": user_request.coc,
                    "company_id": user_request.company_id,
                    "request_id": user_request.id,
                    "user_request": user_request,
                    "self.guest": self.guest,
                    "Id": self.guest.user_id,
                    **request__,
                }
            )
            response_call = service_call[0]

            logger.log(
                "3. Checking for possible errors after the call has been processed...\n",
                "i",
            )
            if "error" in response_call:
                logger.log(
                    f"Something went wrong in the process of "
                    f"rebuying under approval:\n{response_call}\n",
                    "c",
                )

                return (response_call, service_call[1], "close_session")

            if "purchasing_process_started" not in response_call:
                logger.log("3.1 Approving request: updating related request...\n", "i")
                updated_request = user_request.approve(response_call)

                if user_request.service_request_info["service_type"] in [
                    ServiceDocumentType.BALANCE.value,
                    ServiceDocumentType.FINANCIAL_STATEMENT.value,
                ]:
                    enum = ServiceDocumentType(
                        user_request.service_request_info["service_type"]
                    ).name.upper()
                else:
                    enum = ServiceDocumentType(
                        user_request.service_request_info["method_params"][
                            "document_type"
                        ]
                    ).name.upper()
                price = getattr(DocsPricesEnum, enum).value
                if response_call.get("document_id"):
                    self.guest.update(
                        {
                            "ytd_approved_cost": self.guest.ytd_approved_cost + price,
                            "residual_credits": float(self.guest.residual_credits)
                            - price,
                        }
                    )
                response = {
                    **updated_request,
                    "residual_credits": float(round(self.guest.residual_credits, 2)),
                }
                logger.log("Sending user request accepted mail:\n", "i")
                try:
                    mail.send(
                        recipient=requesting_user.email,
                        email_type=MailTemplate.REQUEST_ACCEPTED,
                        message={
                            "coc": response_call["coc"],
                            "user_name": requesting_user.name,
                        },
                    )
                except (SMTPException, socket.error) as e:
                    logger.log(
                        f"Failed to send email to the user for the accepted request: {e}",
                        "c",
                    )

                logger.log(f"Returning response:\n{response}\n", "i")
                return (response, HTTPStatus.CREATED, "close_session")

            logger.log(f"Returning response:\n{response_call}\n", "i")
            return (response_call, HTTPStatus.OK, "close_session")
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class RequestGet(BaseResource):
    """Handles service that is called just by 'approver' users. The method return all the requests
    related to the approver"""

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, *args, **kwargs) -> tuple:
        """
        Endpoint that returns all the requests related to the approver user

        parameters:
          None

        responses:
          200:
            description : list of requests
          422:
            description : input not valid
          404:
            description : no present in the database
        """
        try:
            logger.log("1. Starting process to retrieve requests:\n", "i")

            year_filter = dict(self.request.args)
            user = {"user_id": self.guest.user_id, "id": self.guest.id}
            role = self.guest.role
            filters = dict(self.request.args)
            year_filter = {}

            if "request_id" in filters:
                if filters.get("request_id") is None:
                    return (
                        {
                            "source": Response.api.value,
                            "message": "'request_id' filter must be an UUID type",
                            "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                        },
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        "close_session",
                    )

                logger.log("1.1 Filtering by request id...\n", "i")
                _request: Request = Request.get_by_id(filters.get("request_id"))
                _request = _request.as_dict() if _request is not None else {}

                logger.log(f"Returning response:\n{_request}\n", "i")
                return (
                    _request,
                    HTTPStatus.OK,
                    "close_session",
                )

            if "year" in filters:
                if filters.get("year") is None:
                    return (
                        {
                            "source": Response.api.value,
                            "message": "'year' filter must be an integer value with format: YYYY",
                            "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                        },
                        HTTPStatus.UNPROCESSABLE_ENTITY,
                        "close_session",
                    )
                year_filter = {"year": filters.get("year")}

            if role not in [
                UserResources.ADMIN.name.lower(),
                UserResources.APPROVER.name.lower(),
            ]:
                logger.log("1.1 Query db according to the role 'consumer'...\n", "i")
                data = Request.query_by_criteria(
                    *[Column("user_id") == user["user_id"]]
                )
                results = list()
                for request in data:
                    cls_entity = Company if request.company_id is not None else Person
                    _filter = "company_id" if cls_entity is Company else "person_id"
                    entity = cls_entity.get_by_id(getattr(request, _filter))
                    attr = "vat_code" if cls_entity is Company else "tax_code"
                    name = (
                        entity.name
                        if cls_entity is Company
                        else f"{entity.name} {entity.surname}"
                    )
                    doc_type = {"document_type": None}
                    if request.document_id is not None:
                        document: Document = Document.get_by_document_id(
                            request.document_id
                        )
                        doc_type["document_type"] = document.type
                    elif (
                        request.document_id is None
                        and request.service_request_info is not None
                    ):
                        _type = request.service_request_info["method_params"][
                            "document_type"
                        ]
                        doc_type["document_type"] = ServiceDocumentType(_type).value
                    _d = {
                        **request.as_dict(),
                        "holder": name,
                        attr: getattr(entity, attr),
                        **doc_type,
                    }
                    results.append(_d)

                logger.log(f"Returning response:\n{results}\n", "i")
                return (
                    results,
                    HTTPStatus.OK,
                    "close_session",
                )
            response = Request.get_all(user["id"], user["user_id"], year_filter)

            logger.log(
                f"Returning response according to the role 'approver'/'admin':\n{response}\n",
                "i",
            )
            return (response, HTTPStatus.OK, "close_session")
        except ValueError as ve:
            return (
                {
                    "source": Response.api.value,
                    "message": str(ve),
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                },
                HTTPStatus.UNPROCESSABLE_ENTITY,
                "close_session",
            )
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class RequestDelete(BaseResource):
    """Handles service that is called just by 'consumer' users.
    The method delete from the database the selected resource.
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def delete(self, request_id: UUID, *args, **kwargs) -> dict:
        """
        Endpoint that deletes the request and restore the consumer request amount

        parameters:
          request_id

        responses:
          200:
            description : successful delete
          422:
            description : input not valid
          404:
            description : no present in the database
        """
        try:
            logger.log("1. Starting process to delete requests...\n", "i")
            request: Request = Request.query_by_criteria(*[Column("id") == request_id])[
                0
            ]
            if request is not None:
                logger.log("1.1 Deleting request...\n", "i")
                request.delete(autocommit=True)
                request = Request.query_by_criteria(*[Column("id") == request_id])
                if len(request) == 0:
                    logger.log("1.2 Request deleted\n", "i")
                    logger.log("1.3 Restoring user requests amount...\n", "i")
                    residual_requests = {
                        "residual_requests": self.guest.restore_resources(
                            1, "residual_requests"
                        )
                    }
                    logger.log("1.4 User requests amount restored\n", "i")
                    response = {**residual_requests}
                    return (
                        response,
                        HTTPStatus.OK,
                        "close_session",
                    )
            else:
                return (
                    {
                        "source": Response.api.value,
                        "message": "The request does not exist",
                        "error": HTTPStatus.NOT_FOUND,
                    },
                    HTTPStatus.NOT_FOUND,
                    "close_session",
                )
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
