"""
Module for managing document resources in a RESTful API.

This module provides classes and methods to retrieve and manage documents
associated with users and companies. It includes functionalities to fetch a
specific document, retrieve a user's document list, and filter documents
based on user requests.

Classes:
- DocumentResource: Manages the retrieval of a single document.
- DocumentListResource: Retrieves the list of documents associated with a user.
- DocumentTypesResource: Provides available document types for individuals or companies.

Handled exceptions:
- Returns standard HTTP errors in case of invalid input or missing documents.

"""

from http import HTTPStatus
from typing import List, Union
from uuid import UUID

from sqlalchemy import Column
from sqlalchemy.sql.expression import and_

from app.config import logger
from app.enums.entities import Entities
from app.enums.request.status import StatusEnum
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.person import Person
from app.models.request import Request
from app.resource_access import BaseResource
from app.validate import validate_document


class DocumentResource(BaseResource):
    """
    Resource for managing and retrieving a specific document.

    This class provides an endpoint to fetch a document based on its ID.
    It validates the document and determines the owner (either an individual or a company),
    returning complete information.

    Methods:
    - get(document_id: UUID, **kwargs): Retrieves a document and its owner's details.

    HTTP Responses:
    - 200: Document found and returned successfully.
    - 404: Document not present in the database.
    - 422: Invalid ID or bad request.
    - 500: Internal server error.

    Usage example:
    ```
    GET /document/<document_id>
    ```
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, document_id: UUID, **kwargs) -> tuple:
        """
        Returns a specific document bound to a 'document_id'

        parameters:
          - document_id: 'docuemnt_id' in table 'Document'

        responses:
          200:
            description : document
          422:
            description : input not valid
          404:
            description : no present in the database
        """
        try:
            logger.log("STARTING PROCESS TO RETRIEVE DOC:\n", "i")
            logger.log("1. VALIDATING DOCUMENT BY ID...\n", "i")
            document = validate_document(document_id)
            if isinstance(document, tuple):
                logger.log("DOCUMENT NOT FOUND INTO THE DB\n", "w")
                return document

            doc: Document = document.as_dict() if document is not None else dict()

            entity_mapping = {
                Company: ("vat_code", "company_code"),
                Person: ("tax_code", "tax_code"),
            }
            try:
                cls_entity = Company if doc["company_code"] is not None else Person
            except AttributeError:
                # Handle the case where doc doesn't have the attribute company_code
                cls_entity = Person

            attr, _filter = entity_mapping.get(cls_entity, (None, None))

            if attr is None or _filter is None:
                # Handle the case where cls_entity is neither Company nor Person
                raise ValueError("Invalid entity type")

            entity: List[Union[Company, Person]] = cls_entity.query_by_criteria(
                [Column(attr) == doc[_filter]]
            )
            entity = entity[0]
            name = (
                entity.name
                if cls_entity is Company
                else f"{entity.name} {entity.surname}"
            )
            holder_name = {"holder": name}

            logger.log(f"RETURNING RESPONSE:\n{doc}...\n", "i")
            return ({**doc, **holder_name}, HTTPStatus.OK)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class DocumentListResource(BaseResource):
    """
    Resource for retrieving a list of documents associated with a specific user.

    This class provides an endpoint to fetch all documents that a user has requested
    or acquired. It distinguishes between draft requests and finalized documents.

    Methods:
    - get(**kwargs): Retrieves all documents linked to a user, categorizing them by status.

    HTTP Responses:
    - 200: List of documents returned successfully.
    - 404: No documents found for the user.
    - 422: Invalid request parameters.
    - 500: Internal server error.

    Usage example:
    ```
    GET /documents
    ```

    Additional behavior:
    - If the user is an **approver**, it retrieves documents they have purchased.
    - If the user is a **regular user**, it retrieves documents they have requested.
    - The response includes the document owner's name (company or individual).
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, **kwargs) -> tuple:
        """
        Returns the list of documents bound to a specific user

        parameters:
          None

        responses:
          200:
            description : documents
          422:
            description : input not valid
          404:
            description : no present in the database
        """
        try:
            user_requests_ = (
                Request.query_by_criteria(
                    Column("bought_by") == self.guest.user_id,
                    Column("document_id") is not None,
                )
                if self.guest.role == "approver"
                else Request.query_by_criteria(
                    Column("user_id") == self.guest.user_id,
                    Column("document_id") is not None,
                )
            )

            drafts_, others_ = set(), set()
            for req in user_requests_:
                if req.status == StatusEnum.DRAFT.value:
                    drafts_.add(req.document_id)
                else:
                    others_.add(req.document_id)

            drafts_, others_ = list(drafts_), list(others_)

            documents_rv = []

            for req in Document.get_docs_by_ids_list(drafts_ + others_):
                doc_data = req.as_dict()
                cls_entity = Company if req.company_code is not None else Person
                attr, _filter = (
                    ("vat_code", "company_code")
                    if cls_entity is Company
                    else ("tax_code", "tax_code")
                )
                entity: Union[List[Company], List[Person]] = (
                    cls_entity.query_by_criteria(
                        [Column(attr) == getattr(req, _filter)]
                    )
                )
                entity = entity[0]
                name = (
                    entity.name
                    if cls_entity is Company
                    else f"{entity.name} {entity.surname}"
                )

                # Updated updated_at value if the document was assigned and not bought
                associated_request = next(
                    (r for r in user_requests_ if r.document_id == req.id), None
                )
                if (
                    associated_request
                    and associated_request.action == "assign"
                    and associated_request.status == StatusEnum.DRAFT.value
                    and associated_request.user_id == self.guest.user_id
                ):
                    doc_data["updated_at"] = str(associated_request.updated_at)

                documents_rv.append({**doc_data, "holder": name})

            response = documents_rv

            return (response, HTTPStatus.OK)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class DocumentTypesResource(BaseResource):
    """
    Resource for retrieving available document types for a company or an individual.

    This class provides an endpoint that returns document types associated with
    a given entity, based on its type (company or person).

    Methods:
    - get(entity_type: str, entity_id: UUID, **kwargs): Fetches available document types
      for a given entity.

    HTTP Responses:
    - 200: Document types retrieved successfully.
    - 404: Entity not found in the database.
    - 422: Invalid entity type or request parameters.
    - 500: Internal server error.

    Usage example:
    ```
    GET /document-types?entity_type=company&entity_id=<UUID>
    ```

    Additional behavior:
    - Ensures that `entity_type` is valid before querying the database.
    - Retrieves pending document requests associated with the entity.
    - Provides a formatted response including document status and ownership details.
    """

    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, entity_type: str, entity_id: UUID, **kwargs):
        """
        Retrieves and formats the documents and requests data for the given entity.
        It return the list of documents purchased by the user or available to assign
        since bought by other users.

        :param entity_type: The type of entity ('company' or 'person').
        :param entity_id: The ID of the entity.
        :return: A dictionary with two keys: 'documents' and 'requests'.
        """
        try:
            logger.log(
                "1. Starting process to retrieve entity documents and requests...\n",
                "i",
            )

            if entity_type not in list(Entities.__members__):
                return {
                    "source": Response.api.value,
                    "message": f"Value {entity_type} is not a valid Entities member",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }, HTTPStatus.UNPROCESSABLE_ENTITY

            logger.log(
                "2. Validating entity (company or person) by entity id...\n", "i"
            )
            func = Entities(entity_type).func()
            entity: Union[Company, Person] = func(entity_id)
            if isinstance(entity, tuple):
                logger.log("3. Entity not found in the db\n", "w")
                raise ValueError("Entity not found")

            # Get all request that are pending for documents for the entity
            entity_requests = Request.query_by_criteria(
                and_(
                    Column("person_id" if entity_type == "person" else "company_id")
                    == entity_id,
                    Column("status").in_(
                        [
                            StatusEnum.PENDING.value,
                            StatusEnum.DRAFT.value,
                            StatusEnum.ACCEPTED.value,
                        ]
                    ),
                )
            )

            document_ids = [
                req.service_request_info.get("method_params", {}).get("document_id")
                for req in entity_requests
                if req.service_request_info
                and "method_params" in req.service_request_info
                and "document_id" in req.service_request_info["method_params"]
            ]

            documents_map = {
                str(doc.id): {"code_atto": doc.code_atto, "year": doc.year}
                for doc in Document.get_docs_by_ids_list(document_ids)
            }

            purchased_documents = Document.query_by_criteria(
                (
                    Column("company_code") == str(entity.vat_code)
                    if entity_type == "company"
                    else Column("tax_code") == str(entity.tax_code)
                ),
                (Column("file_url") != ""),
            )

            response = {"documents": [], "requests": []}

            for doc in purchased_documents:

                response["documents"].append(
                    {
                        "id": str(doc.id),
                        "type": doc.type,
                        # Available if the document has already been bought by an other user
                        # Purchased if the document has already been bought by the user
                        "status": (
                            "purchased"
                            if any(
                                req.document_id == doc.id
                                and req.user_id == self.guest.user_id
                                for req in entity_requests
                            )
                            else "available"
                        ),
                        "code_atto": doc.code_atto,
                        "year": doc.year,
                    }
                )

            entity_pending_requests = [
                req for req in entity_requests if req.status == StatusEnum.PENDING.value
            ]

            for request in entity_pending_requests:
                doc_id = request.service_request_info.get("method_params", {}).get(
                    "document_id"
                )
                doc_info = documents_map.get(str(doc_id), {})

                response["requests"].append(
                    {
                        "id": str(request.id),
                        "type": request.service_request_info["method_params"].get(
                            "document_type", "unknown"
                        ),
                        "status": (
                            "requested_by_user"
                            if request.user_id == self.guest.user_id
                            else "requested_by_other"
                        ),
                        "code_atto": doc_info.get("code_atto"),
                        "year": doc_info.get("year"),
                    }
                )

            logger.log(f"3. Returning response:\n{response}\n", "i")
            return response, HTTPStatus.OK

        except Exception as e:
            logger.log(f"4. Error occurred: {e}\n", "e")
            return {
                "source": "Server",
                "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                "message": "Please contact the administrator",
                "details": str(e),
            }, HTTPStatus.INTERNAL_SERVER_ERROR
