"""
Module for handling visura document operations.

Provides functionality for:
- Purchasing new visura documents
- Managing document requests and approvals
- Handling document storage in Azure
- Processing document updates and rebuys
"""

from http import HTTPStatus
from threading import Thread
from time import sleep
from typing import Union
from uuid import UUID

from app.config import Supplier, logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.request.action import ActionEnum
from app.enums.request.status import StatusEnum
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.company import Company
from app.models.document import Document
from app.models.request import Request
from app.models.user import User
from app.resource_access import BaseResource
from app.storage.azstorage import AZStorage
from app.utils import build_headers_params, create_guid, lock_action, utcnow
from app.validate import validate_company


def purchase_visura(
    company: Company,
    document_type: str,
    user: User,
    coc_: str,
    expire: str,
    azstorage: AZStorage,
    action: ActionEnum,
    user_request: Request = None,
    by_approval: bool = False,
    request_id: UUID = None,
    rebuy: bool = False,
    updatable: dict = dict(),
    new_guid: UUID = None,
):
    """
    Funtcion called to parallelize the purchasing request and the insertion into the DB:

        1. first call to data supplier to initialize the purchasing request;
        2. polling data supplier till the content is ready;
        3. save the document inside the DB
    """
    try:
        if by_approval:
            if user_request is None or request_id is None:
                data = {
                    "source": Response.api.value,
                    "message": "Request not processable. Missing one or both of the following \
                        params: 'user_request', 'request_id'",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }
                return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

        logger.log("4.3.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")
        start = utcnow()
        supplier = Supplier("visura")
        a2a = supplier()

        logger.log("4.3.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
        doc = a2a.service.GetChamberOfCommerceCertificate(
            Username=supplier.DATA_SUPPLIER_USERNAME,
            Password=supplier.DATA_SUPPLIER_PASSWORD,
            ApplicationTransactionID=10,
            ProductFormats="PDF",
            InquiryDetails={
                "VisuraATesto": {
                    "TRic": ServiceDocumentType(document_type).get_supplier_value(),
                    "KCF": {"CF": company.tax_code, "Cciaa": company.cciaa},
                }
            },
        )

        if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
            logger.log(
                "DATA SUPPLIER SERVICE CALL FAILS @ "
                "'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                "c",
            )
            data = {
                "source": Response.supplier.value,
                "message": doc.TransactionResponse.Result.Message,
                "error": HTTPStatus.UNPROCESSABLE_ENTITY,
            }
            return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

        ref_id = doc.ProductDetails.DocumentID
        doc = a2a.service.GetChamberOfCommerceCertificate(
            Username=supplier.DATA_SUPPLIER_USERNAME,
            Password=supplier.DATA_SUPPLIER_PASSWORD,
            ApplicationTransactionID=10,
            ProductFormats="PDF",
            InquiryDetails={"DocumentID": ref_id},
        )

        logger.log("4.3.3 STARTING POLLING TOWARDS DATA SUPPLIER...\n", "i")
        wait = False
        end = None
        numb = 1
        while doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
            logger.log(f"4.3.3.1 CALL NUMB. {numb}\n", "i")
            end = utcnow()
            if wait:
                sleep(40)
            doc = a2a.service.GetChamberOfCommerceCertificate(
                Username=supplier.DATA_SUPPLIER_USERNAME,
                Password=supplier.DATA_SUPPLIER_PASSWORD,
                ApplicationTransactionID=10,
                ProductFormats="PDF",
                InquiryDetails={"DocumentID": ref_id},
            )
            wait = True
            numb += 1
        end = utcnow() if end is None else end

        if rebuy:
            logger.log("4.3.4 STARTIG REBUY PROCESS...\n", "i")
            return rebuy_visura_process(
                {
                    "doc": doc.ObjectAttachments.ProductAttachment[0].Content,
                    "azstorage": azstorage,
                    "company_id": company.id,
                    "document": updatable["document"],
                    "by_approval": by_approval,
                    "request_timestamp": start,
                    "user": user,
                    "coc": coc_,
                    "user_request": user_request,
                    "doc_result_code": doc.TransactionResponse.Result.Code,
                    "doc_severity": doc.TransactionResponse.Result.Severity,
                    "new_guid": new_guid,
                    "updated_at": end,
                }
            )

        logger.log("4.3.5 SAVING NEW DOCUMENT OBJECT...\n", "i")
        # Here the document save should be changed to save only
        # the new data to the already created document record
        visura = Document(
            company_code=company.vat_code,
            ref_id=ref_id,
            type=ServiceDocumentType(document_type).value,
            updated_at=end,
        )
        visura.save(autocommit=True)

        logger.log("4.3.6 UPLOADING ON AZURE CONTAINER...\n", "i")
        azurl = azstorage.upload(
            visura,
            doc.ObjectAttachments.ProductAttachment[0].Content,
            ServiceDocumentType.VISURA.value,
        )
        visura.file_url = azurl
        visura.update()

        delta = end - start
        mins, secs = divmod(delta.seconds, 60)
        response = {
            "file_url": visura.file_url,
            "id": str(visura.id),
            "purchasing_process_delta": f"{mins}mm {secs}ss",
            "type": visura.type,
            "vat_code": visura.company_code,
            "holder": company.name,
        }
        httpstatus = HTTPStatus.CREATED

        if not by_approval:
            logger.log("4.3.6.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i")
            request = Request(
                id=new_guid,
                request_timestamp=utcnow(),
                response_timestamp=utcnow(),
                document_id=visura.id,
                purchase=True,
                user_id=user.user_id,
                coc=coc_,
                company_id=company.id,
                result_severity=doc.TransactionResponse.Result.Severity,
                result_code=doc.TransactionResponse.Result.Code,
                action=action,
                updated_at=end,
            )
            request.save(autocommit=True)

        elif by_approval:
            logger.log("4.3.7 UPDATING RELATED REQUEST...\n", "i")
            user_request: Request = Request.get_by_id(request_id)
            user_request.update(
                {
                    "response_timestamp": utcnow(),
                    "purchase": True,
                    "bought_by": user_request.requested_at,
                    "status": StatusEnum.ACCEPTED.value,
                    "result_code": doc.TransactionResponse.Result.Code,
                    "result_severity": doc.TransactionResponse.Result.Severity,
                    "document_id": str(visura.id),
                    "updated_at": end,
                },
                autocommit=True,
            )

        logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
        return ({**response}, httpstatus, expire)
    except Exception as e:
        return (
            {
                "source": "Server",
                "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                "message": "Please contact the administrator",
                "details": e.args[0],
            },
            HTTPStatus.INTERNAL_SERVER_ERROR,
        )


def rebuy_visura_process(call_response: dict) -> dict:
    """
    Process a visura document rebuy request.

    Args:
        call_response: Dictionary containing:
            - doc: Document content
            - azstorage: Azure storage client
            - document: Original document
            - company_id: Company identifier
            - user: Requesting user
            - other rebuy-related parameters

    Returns:
        tuple: (response_data, http_status, session_action)
    """
    logger.log("4.3.4.1 UPLOADING ON AZURE CONTAINER...\n", "i")
    azurl = call_response["azstorage"].upload(
        call_response["document"],
        call_response["doc"],
        "visura",
        overwrite=True,
    )
    visura_to_update: Document = Document.get_by_document_id(
        call_response["document"].id
    )
    visura_to_update.file_url = azurl
    visura_to_update.updated_at = call_response["updated_at"]
    visura_to_update.update(autocommit=True)

    if call_response["by_approval"]:
        logger.log("4.3.4.2 UPDATING REQUEST BECAUSE UNDER APPROVAL...\n", "i")
        user_request: Request = Request.get_by_id(call_response["user_request"].id)
        user_request.update(
            {
                "response_timestamp": utcnow(),
                "purchase": True,
                "bought_by": call_response["user_request"].requested_at,
                "status": StatusEnum.ACCEPTED.value,
                "result_code": call_response["doc_result_code"],
                "result_severity": call_response["doc_severity"],
                "document_id": call_response["document"].id,
                "updated_at": call_response["updated_at"],
            },
            autocommit=True,
        )
    else:
        logger.log("4.3.4.2 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i")
        request = Request(
            id=call_response["new_guid"],
            request_timestamp=call_response["request_timestamp"],
            response_timestamp=call_response["updated_at"],
            document_id=call_response["document"].id,
            user_id=call_response["user"].user_id,
            coc=call_response["coc"],
            action=ActionEnum.REBUY.value,
            company_id=call_response["company_id"],
            purchase=True,
        )
        request.save(autocommit=True)

    response = {
        "file_url": call_response["document"].file_url,
        "id": str(call_response["document"].id),
        "type": call_response["document"].type,
    }
    httpstatus = HTTPStatus.ACCEPTED

    logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
    return (response, httpstatus, "close_session")


class VisuraResource(BaseResource):
    """
    Resource for handling visura document operations.

    Provides endpoints for:
    - Purchasing new visura documents
    - Retrieving existing visura documents
    - Managing document approvals and rebuys

    Attributes:
        check_on_: Credit check requirements
        doc_type_: Supported document types
        access_headers: Required request headers
    """

    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = (
        DocsPricesEnum.VISURA.name,
        DocsPricesEnum.VISURA_HISTORIC.name,
    )
    access_headers: tuple = ("coc",)

    @manage_transaction
    def get(
        self, company_id: UUID, document_type, by_approval: bool = False, **kwargs
    ) -> tuple:
        """Purchase and add new visura documenti inside 'document' table.

        Args:
            company_id (str): id of the related company.
            document_type (str): visura document type to purchase.
            by_approval (bool, optional): to process the methiod as purchasing request or not.
            Defaults to False.

        Returns:
            tuple: response (dict).
        """
        try:
            logger.log("STARTING PROCESS TO SAVE VISURA DOC:\n", "i")
            logger.log(f"1. INTERPRETING ACTIONS FROM kwargs:\n{kwargs}...\n", "i")

            new_guid = None
            action = lock_action(kwargs)
            rebuy = kwargs.get("rebuy")
            if by_approval or rebuy:
                setattr(self, "guest", kwargs["self.guest"])

            logger.log("2. VALIDATING COMPANY BY COMPANY ID...\n", "i")
            company: Union[Company, tuple] = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log("3. BUILDING HEADERS PARAMS...\n", "i")
            vars_ = build_headers_params(self, kwargs, self.access_headers)
            coc_ = vars_["coc"]

            expire = "close_session" if not by_approval else None
            if document_type not in [
                ServiceDocumentType.VISURA.value,
                ServiceDocumentType.VISURA_HISTORIC.value,
            ]:
                data = {
                    "source": Response.api.value,
                    "message": f"'product_id' must be a value of set: '{set([ServiceDocumentType.VISURA.value, ServiceDocumentType.VISURA_HISTORIC.value])}'",
                    "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                }
                return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

            visura = Document.get_by_company_code_and_document_types(
                company_code=company.vat_code,
                document_types=[ServiceDocumentType(document_type).value],
            )

            logger.log("4. CHECKING IF DB DOCUMENT ALREADY ADDED INTO THE DB...\n", "i")
            azstorage: AZStorage = AZStorage()
            response = (
                {
                    "file_url": azstorage.download(visura[0]),
                    "id": str(visura[0].id),
                    "type": visura[0].type,
                    "vat_code": visura[0].company_code,
                    "residual_credits": float(round(self.guest.residual_credits, 2)),
                    "holder": company.name,
                }
                if len(visura)
                else None
            )
            if response is None or rebuy:
                logger.log(
                    "4.1 VISURA DOES NOT EXISTS OR REBUY ASKED. PROCESSING..\n", "i"
                )
                new_guid = create_guid()
                updatable = dict()
                if rebuy:
                    logger.log("4.2 STARTIG REBUY PROCESS...\n", "i")
                    updatable = {
                        "document": visura[0],
                        "kwargs": kwargs.get("user_request"),
                    }
                # Parallelize the purchase process and the insertion into the DB

                logger.log("4.3 EXECUTING PARALLEL TASK TO PURCHASE VISURA...\n", "i")
                thread = Thread(
                    target=purchase_visura,
                    args=(
                        company,
                        document_type,
                        self.guest,
                        coc_,
                        expire,
                        azstorage,
                        action,
                        kwargs.get("user_request"),
                        by_approval,
                        kwargs.get("request_id"),
                        rebuy,
                        updatable,
                        new_guid,
                    ),
                )
                thread.daemon = True
                thread.start()

                if not by_approval:
                    logger.log(
                        "4.4 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    # Remove credits only if API response is success
                    residual_credits = dict()
                    doc_type = ServiceDocumentType(document_type).name.upper()

                    logger.log("4.5 REMOVING CREDITS IF NEEDED...\n", "i")
                    residual_credits = {
                        "residual_credits": self.guest.reduce_resources(
                            getattr(DocsPricesEnum, doc_type).value, "residual_credits"
                        )
                    }

                elif by_approval:
                    logger.log(
                        "4.4 PROCESSING CREDITS REMOVAL BECAUSE UNDER APPROVAL...\n",
                        "i",
                    )
                    # approver = User.query_by_criteria(
                    #     [Column("user_id") == kwargs["user_request"].requested_at]
                    # )
                    # approver: User = approver[0]
                    enum = ServiceDocumentType(document_type).name.upper()
                    price = getattr(DocsPricesEnum, enum).value
                    self.guest.reduce_resources(price, "residual_credits")
                    self.guest.update(
                        {"ytd_approved_cost": self.guest.ytd_approved_cost + price},
                        autocommit=True,
                    )
                    residual_credits = {
                        "residual_credits": float(round(self.guest.residual_credits, 2))
                    }

                request_id = (
                    new_guid if new_guid is not None else kwargs.get("request_id")
                )
                return (
                    {
                        "purchasing_process_started": True,
                        **residual_credits,
                        "request_id": str(request_id),
                    },
                    HTTPStatus.OK,
                    "close_session",
                )
            if kwargs.get("assignable"):
                logger.log("5. ASSIGNING DOCUMENT IF ALREADY EXISTS...\n", "i")
                logger.log("5.1 CREATING REQUEST OBJECT...\n", "i")
                request_assign = Request(
                    request_timestamp=utcnow(),
                    response_timestamp=utcnow(),
                    document_id=visura[0].id,
                    purchase=False,
                    user_id=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                    updated_at=utcnow(),
                )
                request_assign.save()

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, HTTPStatus.OK, expire)
        except Exception as e:
            return (
                {"purchasing_process_started": False, "reason": e.args},
                HTTPStatus.UNPROCESSABLE_ENTITY,
                "close_session",
            )
