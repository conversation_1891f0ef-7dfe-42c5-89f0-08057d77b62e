"""
This module defines the CocResource for handling Certificate of Conformity (CoC)
operations within the API.

The CocResource provides endpoints for retrieving a list of active Certificates of Conformity (CoCs)
and for updating or inserting new CoCs. The GET endpoint allows clients to retrieve all active CoCs
from the database, while the POST endpoint supports batch uploads or updates of CoC records. Each
method includes appropriate error handling and logging for transaction management and auditing.

This module uses the `manage_transaction` decorator to ensure transactional consistency and rollback
in case of errors, helping maintain data integrity. The resource is designed to be used by
authorized clients to manage conformity records efficiently.

Classes:
    CocResource: Provides methods to retrieve, upload, and update Certificates of Conformity.
"""

from http import HTTPStatus

from flask_restful import Resource, request

from app.config import logger
from app.extensions import manage_transaction
from app.models.coc import Coc
from app.models.pushlog import PushLog


class CocResource(Resource):
    """
    CocResource provides endpoints for managing Certificates of Conformity (CoC) in the database.

    This resource supports two main operations:
    - GET: Retrieves a list of all active CoCs.
    - POST: Uploads or updates CoC records in the database.

    Methods:
        get: Retrieves active CoCs in JSON format with a 200 status code on success.
        post: Processes incoming data to update or insert CoC records,
        with status code 200 on success.
    """

    @manage_transaction
    def get(self) -> tuple:
        """
        Retrieves a list of all active CoCs from the database.

        Args:
            kwargs: Additional keyword arguments, not utilized in this method.

        Returns:
            A tuple containing a list of active CoCs in JSON format and the HTTP status code.

        Responses:
            200: List of active CoCs successfully retrieved.
            500: Internal server error occurred.
        """
        try:
            response = {
                "coc_list": [coc.as_dict() for coc in Coc.get_all_active()],
                "last_pushed_date": PushLog.get_last_push_date().isoformat(),
            }
            return (response, HTTPStatus.OK)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    @manage_transaction
    def post(self) -> tuple:
        """
        Handles the uploading and updating of CoCs from incoming data.

        Args:
            kwargs: Additional keyword arguments, not utilized in this method.

        Returns:
            A tuple containing a response message and the HTTP status code.

        Responses:
            200: CoCs successfully processed.
            500: Internal server error occurred.
        """
        try:
            coc_list = request.get_json()
            logger.log("Starting process to update coc", "i")

            push_log = PushLog.create_push()

            Coc.update_or_insert(coc_list)
            logger.log("Coc data update successfull", "i")

            PushLog.complete_push(push_log["id"], len(coc_list))

            return ("Coc list received.", HTTPStatus.OK)

        except Exception as e:
            logger.log("Coc data update failure", "i")

            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": str(e),
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
