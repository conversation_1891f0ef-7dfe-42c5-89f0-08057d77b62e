from datetime import datetime
from http import HTTPStatus
from typing import List, Union
from uuid import UUID

from app.config import Supplier, logger
from app.constants import CONVERION_BT, UNIVERSAL_BT
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.enums.utils import Response
from app.extensions import manage_transaction
from app.models.document import Document
from app.models.request import Request
from app.resource_access import BaseResource
from app.storage.azstorage import AZStorage
from app.utils import build_headers_params, lock_action, utcnow
from app.validate import validate_company, validate_document


class FinancialStatementListResource(BaseResource):
    check_on_: Union[tuple, None] = None
    doc_type_: Union[tuple, None] = None

    @manage_transaction
    def get(self, company_id, **kwargs) -> tuple:
        """
        Endpoint returning all Financial Statements documents in the db associated to this specific company_id.

        parameters:
          - name: company_id
            in : path
            type : UUID
            required : true

        responses:
          200 :
            description: a list of FinancialStatements available in the database
          404 :
            description : no CompanyCards are available on our local db
          422 :
            description : company_id doesn't represent a valid VatCode
        """
        try:
            logger.log("STARTING PROCESS TO SAVE FINANCIAL STATMENTS LIST:\n", "i")
            logger.log("1. VALIDATING COMPANY BY COMPANY ID...\n", "i")

            company = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log(f"1.1 COMPANY FOUND:\n{company}\n", "i")

            financial_statements_list: List[Document] = (
                Document.get_by_company_code_and_document_types(
                    document_types=[ServiceDocumentType.FINANCIAL_STATEMENT.value],
                    company_code=company.vat_code,
                )
            )

            logger.log(
                "2. CHECKING IF DB FINANCIAL STATMENTS ALREADY ADDED INTO THE DB...\n",
                "i",
            )
            response = list()
            httpstatus = None
            expire = None
            if not len(financial_statements_list):
                logger.log("2.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")
                supplier = Supplier("financial_statement")
                a2a = supplier()

                logger.log("2.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.SearchFinancialStatements(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    InquiryDetails={
                        "FinancialStatementsSearchData": {
                            "PCPR": {
                                "PC": company.tax_code,
                                "PR": company.cciaa.upper(),
                                "BT": "A",
                            }
                        }
                    },
                )

                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if len(doc.FinancialStatementsResponse.C) == 0:
                    # @TODO: add to logs and describe the response value doc.FinancialStatementsResponse.C
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'FinancialStatementsResponse.C': NO FINANCIAL STATEMENTS FOUND\n",
                        "w",
                    )
                    return (financial_statements_list, HTTPStatus.OK, "close_session")

                logger.log("2.3 CHECKING FOR OBJECT ATTRS...\n", "i")
                financial_statements_list: list = list()
                if "BH" in doc.FinancialStatementsResponse.C[0]:
                    financial_statements_list = doc.FinancialStatementsResponse.C[0][
                        "BH"
                    ]

                    logger.log(
                        "2.4 CREATING 'Document' INSTANCE FOR EACH FINANCIAL STATMENT RETURNED...\n"
                        "i"
                    )
                    for financial_statement in financial_statements_list:
                        financial_statement = Document(
                            code_atto=financial_statement["AC"],
                            company_code=company.vat_code,
                            type=ServiceDocumentType.FINANCIAL_STATEMENT.value,
                            year=str(financial_statement["CD"].year),
                        )

                        logger.log(
                            f"2.4.1 SAVING FINANCIAL STATMENT:\n{financial_statement}\n"
                            "i"
                        )
                        financial_statement.save()
                        financial_statement_dict = {
                            "code_atto": financial_statement.code_atto,
                            "file_url": True if financial_statement.file_url else False,
                            "id": str(financial_statement.id),
                            "pr_code": company.cciaa.upper(),
                            "type": financial_statement.type,
                            "vat_code": company.vat_code,
                            "year": financial_statement.year,
                        }
                        response.append(financial_statement_dict)
                    return (response, HTTPStatus.CREATED, "close_session")

                logger.log(
                    "2.4 NO FINANCIAL STATMENTS FOUND FROM DATA SUPPLIER SERVICE CALL\n",
                    "w",
                )
                return (financial_statements_list, HTTPStatus.OK, "close_session")

            logger.log(
                "3. FINANCIAL STATEMENTS ALREADY ADDED. CREATING RESPONSE WITH EXISTING DOCS...\n",
                "i",
            )
            if not len(response) and not isinstance(response, dict):
                for financial_statement in financial_statements_list:
                    financial_statement_dict = {
                        "code_atto": financial_statement.code_atto,
                        "file_url": True if financial_statement.file_url else False,
                        "id": str(financial_statement.id),
                        "pr_code": company.cciaa.upper(),
                        "type": financial_statement.type,
                        "vat_code": company.vat_code,
                        "year": datetime.strptime(financial_statement.year, "%Y").year,
                    }
                    response.append(financial_statement_dict)
                    httpstatus = HTTPStatus.OK

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (
                (response, httpstatus, expire)
                if expire is not None
                else (response, httpstatus)
            )
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class FinancialStatementResource(BaseResource):
    check_on_: Union[tuple, None] = ("credits",)
    doc_type_: Union[tuple, None] = (DocsPricesEnum.FINANCIAL_STATEMENT.name,)
    access_headers: tuple = ("coc",)

    @manage_transaction
    def get(
        self, company_id: UUID, document_id: UUID, by_approval: bool = False, **kwargs
    ) -> tuple:
        """
        Endpoint returning a particular FinancialStatement document using vat_code, pr_code and year

        parameters:
        - name: company_id
            in : path
            type : UUID
            required : true
          - name: document_id
            in : path
            type : UUID
            required :true

        responses:
          200 :
            description: a list of CompanyCards was available in the database
          404 :
            description : no CompanyCards are available on our local db
          422 :
            description : vat_code doesn't represent a valid TaxCode
        """
        try:
            logger.log("STARTING PROCESS TO SAVE FINANCIAL STATEMENT DOC:\n", "i")
            logger.log(f"1. INTERPRETING ACTIONS FROM kwargs:\n{kwargs}...\n", "i")

            action = lock_action(kwargs)
            rebuy = kwargs.get("rebuy")
            if by_approval or rebuy:
                setattr(self, "guest", kwargs["self.guest"])

            logger.log("2. VALIDATING COMPANY BY COMPANY ID...\n", "i")
            company = validate_company(company_id)
            if isinstance(company, tuple):
                logger.log("COMPANY NOT FOUND INTO THE DB\n", "w")
                return company

            logger.log("3. LOOKING FOR EXISITNG DOCUMENT...\n", "i")
            financial_statement = validate_document(document_id)
            if isinstance(financial_statement, tuple):
                logger.log("DOCUMENT NOT FOUND INTO THE DB\n", "w")
                return financial_statement

            if company.vat_code != financial_statement.company_code:
                logger.log("WRONG VAT CODE IDENTIFIER\n", "w")
                data = {
                    "source": Response.api.value,
                    "message": f"Document with id '{document_id}' not related to the Company with id '{company_id}'",
                    "error": HTTPStatus.NOT_FOUND,
                }
                return (data, HTTPStatus.NOT_FOUND, "close_session")

            if (
                financial_statement.type
                != ServiceDocumentType.FINANCIAL_STATEMENT.value
            ):
                logger.log("INVALID DOCUMENT TYPE VALUE\n", "i")
                data = {
                    "source": Response.api.value,
                    "message": f"Document with id '{document_id}' it's not of type 'financial_statement'",
                    "error": HTTPStatus.NOT_FOUND,
                }
                return (data, HTTPStatus.NOT_FOUND, "close_session")

            logger.log("4. BUILDING HEADERS PARAMS...\n", "i")
            vars_ = build_headers_params(self, kwargs, self.access_headers)
            coc_ = vars_["coc"]
            expire = "close_session" if not by_approval else None
            now = utcnow()
            bt = (
                UNIVERSAL_BT
                if financial_statement.code_atto not in list(CONVERION_BT.keys())
                else CONVERION_BT[financial_statement.code_atto]
            )

            response = None
            httpstatus = None
            remove_credits = False
            residual_credits = dict()
            azstorage: AZStorage = AZStorage()

            logger.log("5. CHECKING IF DB DOCUMENT ALREADY ADDED INTO THE DB...\n", "i")
            if financial_statement.file_url is None or rebuy:
                logger.log("5.1 INSTANTIATING DATA SUPPLIER OBJECT...\n", "i")

                supplier = Supplier("financial_statement")
                a2a = supplier()

                logger.log("5.2 CALLING DATA SUPPLIER SERVICE...\n", "i")
                doc = a2a.service.GetFinancialStatement(
                    Username=supplier.DATA_SUPPLIER_USERNAME,
                    Password=supplier.DATA_SUPPLIER_PASSWORD,
                    ApplicationTransactionID=10,
                    ProductFormats="PDF",
                    InquiryDetails={
                        "GetFinancialStatementsSearchData": {
                            "PCPR": {
                                "PC": company.vat_code,
                                "PR": company.province_code,
                                "BT": bt,
                            },
                            "BSA": {"RY": {"Y": financial_statement.year}},
                        }
                    },
                )

                if doc.TransactionResponse.Result.Severity in ["Error", "Fatal"]:
                    logger.log(
                        "DATA SUPPLIER SERVICE CALL FAILS @ 'TransactionResponse.Result.Severity' OBJECT ATTR\n",
                        "c",
                    )
                    data = {
                        "source": Response.supplier.value,
                        "message": doc.TransactionResponse.Result.Message,
                        "error": HTTPStatus.UNPROCESSABLE_ENTITY,
                    }
                    return (data, HTTPStatus.UNPROCESSABLE_ENTITY, "close_session")

                if rebuy:
                    logger.log("5.3 STARTIG REBUY PROCESS...\n", "i")
                    return {
                        "doc": doc.ObjectAttachments.ProductAttachment[0].Content,
                        "azstorage": azstorage,
                    }

                logger.log("5.4 UPLOADING ON AZURE CONTAINER...\n", "i")
                financial_statement.updated_at = (
                    doc.TransactionResponse.Details.ResponseTimestamp
                )
                azurl = azstorage.upload(
                    financial_statement,
                    doc.ObjectAttachments.ProductAttachment[0].Content,
                    ServiceDocumentType.FINANCIAL_STATEMENT.value,
                )
                financial_statement.file_url = azurl

                logger.log("5.5 UPDATING RESULT...\n", "i")
                financial_statement.update()

                logger.log("5.6 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    document_id=financial_statement.id,
                    purchase=True,
                    request_timestamp=doc.TransactionResponse.Details.RequestTimestamp,
                    response_timestamp=doc.TransactionResponse.Details.ResponseTimestamp,
                    result_code=doc.TransactionResponse.Result.Code,
                    result_severity=doc.TransactionResponse.Result.Severity,
                    user_id=self.guest.user_id,
                    bought_by=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.6.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = (
                    {
                        "vat_code": financial_statement.company_code,
                        "file_url": financial_statement.file_url,
                        "id": str(financial_statement.id),
                        "type": financial_statement.type,
                        "code_atto": financial_statement.code_atto,
                    }
                    if not by_approval
                    else request.as_dict()
                )
                httpstatus = HTTPStatus.CREATED
                remove_credits = True

            if response is None:
                logger.log(
                    "5.1 DOCUMENT ALREADY EXISTS: CREATING NEW REQUEST OBJECT TO SAVE...\n"
                    "i"
                )
                logger.log("5.2 CREATING REQUEST OBJECT...\n", "i")
                request = Request(
                    document_id=financial_statement.id,
                    purchase=False,
                    request_timestamp=now,
                    response_timestamp=utcnow(),
                    user_id=self.guest.user_id,
                    coc=coc_,
                    company_id=company.id,
                    action=action,
                )
                if not by_approval:
                    logger.log(
                        "5.2.1 SAVING REQUEST BECAUSE NOT UNDER APPROVAL...\n", "i"
                    )
                    request.save()

                response = {
                    "vat_code": financial_statement.company_code,
                    "file_url": azstorage.download(financial_statement),
                    "id": str(financial_statement.id),
                    "type": financial_statement.type,
                    "code_atto": financial_statement.code_atto,
                }

                residual_credits = {
                    "residual_credits": float(round(self.guest.residual_credits, 2))
                }
                httpstatus = HTTPStatus.OK

            # Remove credits only if API response is success
            if remove_credits:
                logger.log("6 REMOVING CREDITS IF NEEDED...\n", "i")
                residual_credits = {
                    "residual_credits": self.guest.reduce_resources(
                        DocsPricesEnum.FINANCIAL_STATEMENT.value, "residual_credits"
                    )
                }
            response = {**response, **residual_credits}

            logger.log(f"RETURNING RESPONSE:\n{response}\n", "i")
            return (response, httpstatus, expire)
        except Exception as e:
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
