"""
This module provides the implementation of the `BaseResource` class,
which serves as the base class for API resources in the Flask application.

The `BaseResource` class extends the `Resource` class from Flask-RESTful and adds
common functionality such as request handling, authentication, error management,
and validation of headers and query string arguments.

Classes:
    - BaseResource: Provides base functionalities for API resources, including dispatching
      requests, handling errors, and validating request parameters.

Imports:
    - json: Standard library for JSON operations.
    - traceback: Standard library for error tracebacks.
    - HTTPStatus: Standard library for HTTP status codes.
    - Resource, request: Imported from flask_restful for RESTful API handling.
    - UserAuth, InvalidAuthenticationToken, InvalidAuthorizationToken:
    Custom authentication classes from `app.auth.auth`.
    - logger: Custom logger from `app.config`.
    - DocsPricesEnum, ServiceDocumentType: Enumerations for document
    types and pricing from `app.enums.document.document`.
    - manage_transaction: Custom transaction management decorator from `app.extensions`.
    - User: User model imported from `.models.user`.

Example:
    This module is intended to be inherited by other resource classes, for example:

    ```
    class MyResource(BaseResource):
        def get(self):
            return {"message": "Hello, World!"}
    ```
"""

import json
import os
import traceback
from http import HTTPStatus

from flask_restful import Resource, request

from app.auth.auth import (InvalidAuthenticationToken,
                           InvalidAuthorizationToken, UserAuth)
from app.config import logger
from app.enums.document.document import DocsPricesEnum, ServiceDocumentType
from app.extensions import manage_transaction
from app.models.user import User


class BaseResource(Resource):
    """
    Class handles request processing, authentication, and error handling for API resources.

    Attributes:
        access_headers (list): A list of required headers that must be present in the request.
        access_args (list): A list of required query string arguments that must be present

    Methods:
        dispatch_request: Dispatches the incoming request to the appropriate resource method.

    """

    def __init__(self):
        self.cache = None
        self.email_data = None
        self.guest: User = None
        self.session_cookie: str
        self.ttl = None

    @manage_transaction
    def dispatch_request(self, *args, **kwargs) -> object:
        try:

            headers = request.headers
            headers_str = " | ".join([f"{k}: {v}" for k, v in headers.items()])
            logger.log(f"Request headers: {headers_str}\n", "i")

            user, self.cache, self.session_cookie, self.ttl = UserAuth(
                request=request
            ).get_auth_info()
            self.guest = user

            kwargs["user_role"] = user.role

            # Request body
            body: dict = json.loads(request.data) if len(request.data) else dict()
            logger.log(f"Request body:\n{body}\n", "i")

            # Request query and path params
            args = request.args
            logger.log(f"Request args:\n{args}\n", "i")

            # Request method params
            method_params = request.view_args
            logger.log(f"Request method params:\n{method_params}\n", "i")

            assignable = False
            if "Assignable" in list(headers.keys()):
                assignable = (
                    True if headers["assignable"].lower() == "true" else assignable
                )

            if self.session_cookie is None:
                self.validate_headers(headers)
            self.validate_content_type()
            # self.validate_querystring_args(args)

            if user is not None and self.check_on_ is not None:
                self.validate_credits_and_requests(
                    user, body, method_params, assignable
                )

            # Add request params to each method -> avoids multiple import later
            setattr(self, "request", request)

            # Add request to kwargs -> when using methods as funcs
            kwargs["request__"] = {
                str(k).lower().replace("-", "_"): v for k, v in request.headers.items()
            }
            kwargs["assignable"] = assignable

            logger.log(f"Request dispatched with kwargs:\n{kwargs}\n", "i")

            response = super(BaseResource, self).dispatch_request(*args, **kwargs)

            if isinstance(response, tuple):
                data, status_code, headers = (
                    response if len(response) == 3 else (*response, {})
                )
                headers = {}
            else:
                data = response
                status_code = 200
                headers = {}

            if self.session_cookie and self.cache.get(self.session_cookie) is None:
                headers = self.set_session_cookie(headers)

            self.cache.set(
                self.session_cookie,
                json.dumps(self.guest.as_dict()),
                ex=self.ttl,
            )
            logger.log(
                f"Updating cache entry for user { self.guest.as_dict()['id'] }",
                "i",
            )

            return data, status_code, headers

        except ValueError as e:
            logger.log(f"ValueError:\n{e}\n", "w")
            return self._return_exception(e)
        except (
            InvalidAuthorizationToken,
            InvalidAuthenticationToken,
        ) as e:
            logger.log(
                f"InvalidAuthorizationToken / InvalidAuthenticationToken :\n{e}\n",
                "w",
            )
            return ({"Api root auth": e.args[0]}, HTTPStatus.UNAUTHORIZED)
        except Exception as e:
            logger.log(
                f"Not handled error:\n{e}\nstack:\n{traceback.format_exc()}\n", "c"
            )
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    def _return_exception(self, exception: Exception) -> tuple:
        err = (
            exception.args[0]
            if isinstance(exception.args[0], dict)
            else self._compose_err_msg("", msg_=exception.args[0])
        )
        status_code = (
            exception.args["error"]
            if "error" in exception.args
            else HTTPStatus.BAD_REQUEST
        )
        return (err, status_code)

    def _compose_err_msg(self, attr: str, msg_: str = None) -> dict:
        msg = f"'{attr}' header must be provided" if msg_ is None else msg_
        return {
            "source": "API root params validation",
            "error": HTTPStatus.BAD_REQUEST,
            "message": msg,
        }

    def validate_headers(self, headers: dict) -> None:
        """Headers checks: they must be provided

        param: headers [dict]
        """
        for tag in self.access_headers:
            if headers.get(tag) is None:
                raise ValueError(self._compose_err_msg(tag))

    def validate_querystring_args(self, args: dict) -> None:
        """Headers checks: they must be provided

        param: args [dict]
        """
        for tag in self.access_args:
            if args.get(tag) is None:
                raise ValueError(self._compose_err_msg(tag))

    def validate_content_type(self) -> object:
        """
        Validates the Content-Type of the incoming request.

        This method ensures that the request's Content-Type is 'application/json'
        for methods that typically include a body (POST, PUT, PATCH). If the Content-Type
        is not as expected, a ValueError is raised, which should be caught and handled
        appropriately by the calling method.

        Raises:
            ValueError: If the Content-Type is not 'application/json' for methods
                        that require a body.
        """
        if request.method in ["POST", "PUT"]:
            if request.content_type != "application/json":
                raise ValueError("Invalid content type, must be application/json")

    def validate_credits_and_requests(
        self, user: object, body: dict, method_params: dict, assignable: bool = False
    ) -> None:
        """DB check on 'requests' and 'credits' fields. This is done here
        to prevent any query to the data supplier.
        """
        logger.log("Starting validation of credits and requests...\n", "i")

        if not assignable:
            reqs_: str = "requests"
            crds_: str = "credits"
            resourcses_: tuple = (crds_, reqs_)

            if hasattr(self, "check_on_"):
                if self.check_on_ is not None:
                    if not set(self.check_on_).issubset(set(resourcses_)):
                        raise ValueError(
                            {
                                "source": "API root params validation",
                                "error": HTTPStatus.BAD_REQUEST,
                                "message": f"Types in '{self.check_on_}' are not valid types",
                            }
                        )

            for type_ in self.check_on_ if self.check_on_ is not None else list():
                if "service_request_info" in body:
                    logger.log("...Purchase request: exit\n", "i")
                    return
                document_type = method_params.get("document_type")
                if document_type is None:
                    document_type = getattr(self, "doc_type_")[0].lower()

                logger.log(f"1. Document type:\n{document_type}\n", "i")

                enum = (
                    ServiceDocumentType(document_type).name.upper()
                    if not hasattr(self, "doc_type_name_referance_")
                    else getattr(
                        ServiceDocumentType, document_type.upper()
                    ).name.upper()
                )
                price = getattr(DocsPricesEnum, enum).value

                logger.log(f"2. Document price:\n{price}\n", "i")

                pass_check = (
                    bool(float(user.residual_credits) > price)
                    if type_ == crds_
                    else bool(float(user.residual_requests) > 0)
                )
                if not pass_check:
                    raise ValueError(
                        {
                            "source": "API root params validation",
                            "error": HTTPStatus.FORBIDDEN,
                            "message": f"No residual {type_} available",
                        }
                    )

        logger.log("Validation of credits and requests ended\n", "i")

    def set_session_cookie(self, headers: dict) -> dict:
        """
        Sets the session cookie with the appropriate logic based on the environment.

        Args:
            headers (dict): The set of headers of the response.

        Returns:
            dict: The updated headers with the session cookie set.
        """
        set_cookie_header = (
            f"session_cookie={self.session_cookie}; HttpOnly; Max-Age={self.ttl}"
        )
        if os.getenv("FLASK_ENV") == "production":
            set_cookie_header += "; SameSite=Lax"
        else:
            set_cookie_header += "; Secure; SameSite=None"

        headers["Set-Cookie"] = set_cookie_header

        return headers
