"""
This module provides essential utility classes and functions that are used across the application.

Classes:
    Supplier:
        Interacts with external data supplier API, managing the connection and service invocation.

    AuthConfig:
        Configures and manages authorization/authentication settings, including JWT validation and
        user retrieval.

    Logger:
        Initializes and manages the logging setup for the application, using <PERSON>gu<PERSON> for enhanced
        logging in development environments and Azure logging in production environments.

    MailConfig:
        Generates email content based on predefined templates and user-supplied variables.
"""

import logging
import os
import sys
from http import HTTPStatus
from typing import Any, Dict, Literal, Tuple, Union
from uuid import UUID

import requests
from azure.monitor.opentelemetry import configure_azure_monitor
from loguru import logger as loguru_logger
from opencensus.ext.azure.log_exporter import <PERSON><PERSON><PERSON>ogHand<PERSON>
from sqlalchemy.orm.session import Session
from zeep import Client, Settings

from app.enums.mail.mail import MailTemplate


class Supplier:
    """
    Supplier class that interacts with an external data supplier API.

    This class is designed to initialize and manage the connection to a web service
    provided by an external data supplier. It handles the setup of required settings,
    and provides a mechanism to invoke the service using a specified configuration.

    Attributes:
        DATA_SUPPLIER_PASSWORD (str): The password for authenticating with the data supplier.
        DATA_SUPPLIER_USERNAME (str): The username for authenticating with the data supplier.
        BASE_URL_ (str): The base URL for the supplier's web services.
        urls_ (dict): A dictionary containing service-specific endpoints.

    Methods:
        __init__(service: str):
            Initializes the Supplier instance with the specified service and
            sets up the required settings.

        __call__(apply_settings: bool = True) -> Client:
            Invokes the service specified during initialization, applying settings if required.
            Returns a Client instance or an error response in case of failure.
    """

    BASE_URL_ = os.getenv("SUPPLIER_BASE_URL")
    if not BASE_URL_:
        raise RuntimeError("Missing SUPPLIER_BASE_URL environment variable")

    DATA_SUPPLIER_PASSWORD: str = os.getenv("DATA_SUPPLIER_PASSWORD")
    DATA_SUPPLIER_USERNAME: str = os.getenv("DATA_SUPPLIER_USERNAME")
    urls_: dict = {
        "balance": os.getenv("BALANCE_ROOT"),
        "company_card": os.getenv("COMPANYCARD_ROOT"),
        "company": os.getenv("COMPANY_ROOT"),
        "financial_statement": os.getenv("FINANCIALSTATEMENT_ROOT"),
        "legal_procedures": os.getenv("LEGALPROCEDURES_ROOT"),
        "person_report": os.getenv("PERSONREPORT_ROOT"),
        "person": os.getenv("PERSON_ROOT"),
        "representative_card": os.getenv("REPRESENTATIVECARD_ROOT"),
        "shareholders": os.getenv("SHAREHOLDERS_ROOT"),
        "shares": os.getenv("SHARES_ROOT"),
        "visura": os.getenv("VISURA_ROOT"),
        "report": os.getenv("REPORT_ROOT"),
    }

    @staticmethod
    def validate_certificate() -> None:
        """
        Validates the existence of the required certificate file in the app/assets/certs directory.

        This method checks for the certificate file specified in the DATA_SUPPLIER_CERTIFICATE
        environment variable within the app/assets/certs directory. If the certificate is not
        found, it logs critical errors and terminates Flask to prevent startup with
        missing certificates.

        Raises:
            SystemExit: If the certificate file is not found or DATA_SUPPLIER_CERTIFICATE is not set.
        """
        try:
            # Get certificate name from environment
            cert_name = os.getenv("DATA_SUPPLIER_CERTIFICATE")
            if not cert_name:
                error_msg = "DATA_SUPPLIER_CERTIFICATE environment variable is not set"
                logger.log(
                    f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
                )
                logger.log(
                    "Flask application cannot start without certificate configuration. Terminating...",
                    level="c",
                )
                logger.log(
                    "Please set the DATA_SUPPLIER_CERTIFICATE environment variable.",
                    level="c",
                )
                sys.exit(1)

            # Build certificate path
            app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cert_dir = os.path.join(app_dir, "app", "assets", "certs")
            cert_path = os.path.join(cert_dir, cert_name)

            logger.log(f"Validating certificate existence: {cert_path}", level="d")

            # Check if certs directory exists
            if not os.path.exists(cert_dir):
                error_msg = f"Certificate directory does not exist: {cert_dir}"
                logger.log(
                    f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
                )
                logger.log(
                    "Flask application cannot start without certificate directory. Terminating...",
                    level="c",
                )
                logger.log(f"Please create the directory: {cert_dir}", level="c")
                sys.exit(1)

            # Check if certificate file exists
            if not os.path.exists(cert_path):
                error_msg = f"Certificate file not found: {cert_path}"
                logger.log(
                    f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
                )
                logger.log(
                    "Flask application cannot start without the required certificate. Terminating...",
                    level="c",
                )
                logger.log(
                    f"Please ensure the certificate file '{cert_name}' exists in {cert_dir}",
                    level="c",
                )
                sys.exit(1)

            # Check if it's actually a file (not a directory)
            if not os.path.isfile(cert_path):
                error_msg = f"Certificate path exists but is not a file: {cert_path}"
                logger.log(
                    f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
                )
                logger.log(
                    "Flask application cannot start with invalid certificate path. Terminating...",
                    level="c",
                )
                sys.exit(1)

            # Optional: Check file size (empty files might indicate issues)
            file_size = os.path.getsize(cert_path)
            if file_size == 0:
                error_msg = f"Certificate file is empty: {cert_path}"
                logger.log(
                    f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
                )
                logger.log(
                    "Flask application cannot start with empty certificate file. Terminating...",
                    level="c",
                )
                sys.exit(1)

            logger.log(
                f"Certificate validation successful: {cert_name} ({file_size} bytes)",
                level="i",
            )

        except Exception as e:
            error_msg = f"Unexpected error during certificate validation: {e}"
            logger.log(
                f"CRITICAL: Certificate validation failed - {error_msg}", level="c"
            )
            logger.log(
                "Flask application cannot start due to certificate validation error. Terminating...",
                level="c",
            )
            sys.exit(1)

    def __init__(self, service: str) -> None:
        if service not in self.urls_:
            raise ValueError(f"Unknown service '{service}' passed to Supplier")
        self.settings = Settings(
            strict=False, xml_huge_tree=True, xsd_ignore_sequence_order=True
        )
        self.service = service
        self.client = None

    def __call__(self, apply_settings: bool = True) -> Client:
        logger.log("Init data supplier...\n", "i")
        try:

            return Client(
                wsdl=self.BASE_URL_ + self.urls_[self.service],
                settings=None if not apply_settings else self.settings,
            )
        except Exception as e:
            logger.log(
                f"Critical error while init data supplier client class:\n{e}\n", "c"
            )
            return (
                {
                    "source": "Server",
                    "error": HTTPStatus.INTERNAL_SERVER_ERROR,
                    "message": "Please contact the administrator",
                    "details": e.args[0],
                },
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )


class AuthConfig:
    """
    Config class that set all the needed variables to configure
    authorization/authentication steps later on.

    For a DEV environment, the configuration flag the variable 'dev_env'
    to True. In this way, the Auth class does not perform authorization
    or authentication steps.
    """

    ISSUER: str = os.getenv("ISSUER_URL")
    jwks: dict = {}
    VALID_AUDIENCES: tuple = (os.getenv("AZURE_CLIENT_ID"),)
    GRAPH_URL: str = os.getenv("GRAPH_URL")
    dev_env: bool = True if os.getenv("ENVIRONMENT") == "development" else False
    auth_required: bool = os.getenv("auth_required")
    is_active: bool = False
    ADMIN_ID: UUID = UUID(os.getenv("DB_ADMIN_ID"))
    ADMIN_USERID: str = os.getenv("ADMIN_USERID")
    ADMIN_USER = None
    APPROVER_ID: UUID = UUID(os.getenv("DB_APPROVER_ID"))
    APPROVER_USERID: str = os.getenv("APPROVER_USERID")
    APPROVER_USER = None
    CONSUMER_ID: UUID = UUID(os.getenv("DB_CONSUMER_ID"))
    CONSUMER_USERID: str = os.getenv("CONSUMER_USERID")
    CONSUMER_USER = None

    def __init__(self) -> None:
        if not self.dev_env:
            self.auth_required = False
            self.is_active = True
        else:
            self.is_active = True

    def __setattr__(self, __name: str, __value: Any) -> None:
        self.__dict__[__name] = __value

    def fetch_jwks(self):
        """
        Fetches the JSON Web Key Set (JWKS) from the specified URL and updates the JWKS attribute.

        This method sends an HTTP GET request to the JWKS URL defined in the environment
        variables. If the request is successful and the response contains a valid list of keys,
        the JWKS attribute is updated. If the response is not successful or the keys are invalid,
        an exception is raised.

        Raises:
            ValueError: If the JWKS URL does not return a list of keys.
            ConnectionError: If the HTTP request to the JWKS URL fails.

        Returns:
            None
        """
        jwks_url = os.getenv("JWKS_URL")
        response = requests.get(jwks_url, timeout=1000)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data.get("keys"), list):
                self.jwks = data
            else:
                raise ValueError("JWKS URL did not return a list of keys")
        else:
            raise ConnectionError(f"Failed to fetch JWKS: HTTP {response.status_code}")

    def get_user(
        self, user: object, session: Session, _id: UUID
    ) -> Union[object, None]:
        """
        Retrieves a user object from the database based on the given user ID.

        This method queries the database for a user with the specified UUID. If a matching
        user is found, the user object is returned; otherwise, it returns None.

        Args:
            user (object): The user model class to query.
            session (Session): The SQLAlchemy session used to perform the query.
            _id (UUID): The unique identifier of the user to retrieve.

        Returns:
            object or None: The user object if found, otherwise None.
        """
        return session.query(user).filter(user.id == _id).one_or_none()


class Logger:
    """
    Logger class that initializes the logging handler according
    to the running environment, using Loguru for enhanced logging.
    """

    def __init__(self) -> None:
        self.dev_env: bool = os.getenv("ENVIRONMENT") == "development"
        self.__log_format: str = "🚀 %(levelname)s - %(asctime)s - %(message)s"
        self.__date_format: str = "%H:%M:%S %d-%m-%y"

        # Set up Loguru logger for both dev and prod environments
        self._setup_loguru_logger()

        if not self.dev_env:
            self._add_azure_log_handler()

        # Optionally configure SQLAlchemy logging for development
        if self.dev_env:
            self._configure_sqlalchemy_logging()

    def _setup_loguru_logger(self) -> None:
        """
        Setup Loguru logger configuration without file logging (only console logging).
        """
        # Remove the default Loguru handler to avoid duplicate logs
        loguru_logger.remove()

        # Add a console sink for colorized output in the terminal
        loguru_logger.add(
            sys.stdout,
            colorize=True,
            format="<level>🚀 {level}</level> - {time:HH:mm:ss DD-MM-YY} - <level>{message}</level>",
            level="DEBUG",
        )

    def _add_azure_log_handler(self) -> None:
        """
        Adds AzureLogHandler to Loguru logger for production environment.
        """
        azure_handler = AzureLogHandler(
            connection_string=os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING")
        )
        # Wrap AzureLogHandler with a standard handler to work with Loguru
        loguru_logger.add(
            azure_handler.emit, level="INFO", format="<level>{message}</level>"
        )

        configure_azure_monitor(enable_live_metrics=True)

    def _configure_sqlalchemy_logging(self) -> None:
        """
        Configures the SQLAlchemy logger to use the same settings as the main application logger.
        """
        sqlalchemy_logger = logging.getLogger("sqlalchemy.engine")
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(fmt=self.__log_format, datefmt=self.__date_format)
        handler.setFormatter(formatter)
        sqlalchemy_logger.addHandler(handler)
        sqlalchemy_logger.setLevel(logging.INFO)

    def log(self, msg: str, level: str = Literal["d", "i", "w", "c"]) -> None:
        """
        Logs a message at the specified level using Loguru.

        Args:
            msg (str): The message to log.
            level (str): The level at which to log the message. Valid values are:
                - "d": Debug level (detailed information, typically of interest only
                    when diagnosing problems).
                - "i": Info level (confirmation that things are working as expected).
                - "w": Warning level (an indication that something unexpected happened).
                - "c": Critical level (a serious error, indicating that the program itself
                    may be unable to continue running).

        Returns:
            None
        """
        if level == "d":
            loguru_logger.debug(msg)
        elif level == "i":
            loguru_logger.info(msg)
        elif level == "w":
            loguru_logger.warning(msg)
        elif level == "c":
            loguru_logger.critical(msg)
        else:
            loguru_logger.critical(msg)


# Initialize logger
logger = Logger()


class MailConfig:
    """
    Provides email content based on the MailTemplate enum and user-supplied variables.

    Methods:
    - `get_email_content`: Retrieves and formats the subject and message content
    for a specified email type.
    """

    @classmethod
    def get_email_content(
        cls, email_type: MailTemplate, message: dict
    ) -> Union[Tuple[str, str], Tuple[Dict[str, Union[str, int]], HTTPStatus]]:
        """
        Retrieves and formats the subject and message content for the specified email type.

        Args:
            email_type (MailTemplate): The type of email (e.g., MailTemplate.USER_REGISTRATION).
            message (dict): A dictionary containing the message content and the variables to
            substitute in the message.

        Returns:
            tuple: The formatted subject and message content.

        Raises:
            ValueError: If any required variables are missing from the
            provided variables dictionary.
        """
        subject_template = email_type.get_subject()
        message_template = email_type.get_message()

        required_variables = email_type.get_variables()
        missing_variables = []

        for var in required_variables:
            keys = var.split(".")
            temp_message = message
            for key in keys:
                if isinstance(temp_message, dict) and key in temp_message:
                    temp_message = temp_message[key]
                else:
                    missing_variables.append(var)
                    break

            if not temp_message:
                missing_variables.append(var)

        if missing_variables:
            logger.log(
                f"Missing variables in the message: {', '.join(missing_variables)}. \
                    Email Type: {email_type}"
            )
            return (
                {
                    "source": "Client",
                    "error": HTTPStatus.BAD_REQUEST,
                    "message": f"Missing required variables in the message \
                        : {', '.join(missing_variables)}",
                },
                HTTPStatus.BAD_REQUEST,
            )

        subject = subject_template.format(**message)
        formatted_message = message_template.format(**message)

        return subject, formatted_message
