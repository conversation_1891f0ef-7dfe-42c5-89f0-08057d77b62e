from typing import Union
from uuid import UUID

from app.models.company import Company
from app.models.document import Document
from app.models.person import Person
from app.utils import _compose_err_msg


def validate_company(company_id: UUID) -> Union[Company, tuple]:
    """
    Validates the existence of a company with the given ID.

    Args:
        company_id (UUID): The ID of the company to validate.

    Returns:
        Union[Company, tuple]: The Company instance if found, otherwise an error message tuple.
    """
    company = Company.find(company_id)
    if company is None:
        return _compose_err_msg(f"Company with id '{company_id}' not found")
    return company


def validate_person(person_id: UUID) -> Union[Person, tuple]:
    """
    Validates the existence of a person with the given ID.

    Args:
        person_id (UUID): The ID of the person to validate.

    Returns:
        Union[Person, tuple]: The Person instance if found, otherwise an error message tuple.
    """
    person = Person.find(person_id)
    if person is None:
        return _compose_err_msg(f"Person with id '{person_id}' not found")
    return person


def validate_document(document_id: UUID) -> Union[Document, tuple]:
    """
    Validates the existence of a document with the given ID.

    Args:
        document_id (UUID): The ID of the document to validate.

    Returns:
        Union[Document, tuple]: The Document instance if found, otherwise an error message tuple.
    """
    document = Document.find(document_id)
    if document is None:
        return _compose_err_msg(f"Document with id '{document_id}' not found")
    return document
