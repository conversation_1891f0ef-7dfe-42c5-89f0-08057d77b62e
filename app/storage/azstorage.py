"""
This module provides the `AZStorage` class, which manages interactions with Azure Blob Storage
for storing and retrieving documents.

The class handles tasks such as uploading and downloading documents to and from Azure Blob Storage.
The module also includes functionality for managing documents in a local development environment,
ensuring that operations such as file creation, retrieval, and storage are seamlessly integrated
with the Azure storage solution.

Classes:
    - AZStorage: Handles Azure Blob Storage interactions, including file uploads and downloads.

Imports:
    - base64: Provides base64 encoding and decoding functions.
    - io: Provides core tools for working with streams.
    - os: Provides functions for interacting with the operating system.
    - zipfile: Provides tools for creating, reading, writing, appending, and extracting ZIP files.
    - HTTPStatus: Contains standard HTTP status codes.
    - Union: Provides type hinting for functions that can return more than one type of value.
    - BlobServiceClient, ContentSettings: Provides classes for interacting with Azure Blob Storage.
    - logger: Custom logger for logging operations.
    - ServiceDocumentType: Enumerations for document types.
    - Document: Document model used for database interactions.

Example:
    ```
    storage = AZStorage()
    file_url = storage.upload(document, new_content, document_type)
    downloaded_file = storage.download(document)
    ```
"""

import base64
import io
import os
import zipfile
from typing import Union

from azure.storage.blob import BlobServiceClient, ContentSettings

from app.config import logger
from app.enums.document.document import ServiceDocumentType
from app.models.document import Document


class AZStorage:
    """
    Class that handles Azure storage service.
    """

    _container_name = "documents"
    _doc_type_path = {
        ServiceDocumentType.BALANCE.value: "balances/",
        ServiceDocumentType.COMPANY_CARD.value: "company_cards/",
        ServiceDocumentType.FINANCIAL_STATEMENT.value: "financial_statements/",
        ServiceDocumentType.LEGAL_PROCEDURES_DEED.value: "legal_procedures_deeds/",
        "report": "reports/",
        "person_report": "person_reports/",
        "representative_card": "representative_cards/",
        ServiceDocumentType.SHAREHOLDERS.value: "shareholders/",
        ServiceDocumentType.SHARES.value: "shares/",
        ServiceDocumentType.VISURA.value: "visure/",
    }

    def __init__(self):
        try:
            if os.getenv("ENVIRONMENT") != "development":

                logger.log(
                    "Creating blobserviceclient from a connection string:\n", "i"
                )
                self.blob_service_client = BlobServiceClient.from_connection_string(
                    conn_str=os.getenv("AZURE_STORAGE_CONN_STRING")
                )

                if self._container_name not in [
                    c["name"]
                    for c in list(
                        self.blob_service_client.list_containers(include_metadata=True)
                    )
                ]:
                    logger.log(
                        f"Creating new container with name:\n{self._container_name}\n",
                        "i",
                    )

                    self.blob_service_client.create_container(self._container_name)

                logger.log("Getting container:\n", "i")
                containers = list(
                    self.blob_service_client.list_containers(include_metadata=True)
                )
                self.container = list(
                    filter(
                        lambda container: container["name"] == self._container_name,
                        containers,
                    )
                )[0]
                logger.log(f"Stored container:\n{self.container}\n", "i")

        except ConnectionError as e:
            raise ConnectionError from e

    def upload(
        self,
        document: Document,
        new_content: str,
        document_type: str,
        overwrite: bool = False,
    ) -> str:
        """
        Upload document content into the Azure container.
        The document is saved as .pdf file.

        Args:
            document (Document): the Document record of the related content.
            new_content (str): content got by data supplier.
            document_type (str): the type of the document.
            overwrite (Optional[bool]): if file should be overwritten this should be true

        Returns:
            str: full Azure storage URL.
        """
        max_size = 50_000_000  # 50MB

        decoded = base64.b64decode(new_content)
        file_content = io.BytesIO(decoded)

        try:
            if zipfile.is_zipfile(file_content):
                with zipfile.ZipFile(file_content) as z:
                    if len(z.filelist) != 1 or not z.filelist[
                        0
                    ].filename.lower().endswith(".pdf"):
                        raise ValueError("ZIP must contain exactly one PDF file")
                    blob = z.read(z.filelist[0])
            else:
                blob = decoded

            if not blob.startswith(b"%PDF-") or len(blob) > max_size:
                raise ValueError("Invalid file type or size exceeds limit")

            logger.log("File validated and ready for upload", "i")

            if os.getenv("ENVIRONMENT") == "development":
                tmp = "temporary"
                cwd = os.getcwd()
                tmppath = f"{cwd}/{tmp}"

                if not os.path.isdir(tmppath):
                    logger.log("4. Path does not exists: creting folder:\n")
                    os.mkdir(tmppath)

                filename = f"{document.id}.pdf"
                with open(file=f"{tmppath}/{filename}", mode="wb") as f:
                    f.write(blob)

                logger.log("4. File created and saved\n", "i")
                return os.path.join(cwd, tmp, filename)

            sub_folder = f"{self._doc_type_path[document_type]}"
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container.name, blob=f"{sub_folder}{document.id}.pdf"
            )

            logger.log(f"4. Uploading document in:\n{sub_folder}:\n", "i")
            content_settings = ContentSettings(content_type="application/pdf")
            blob_client.upload_blob(
                blob, overwrite=overwrite, content_settings=content_settings
            )

            logger.log("Document successfully uploaded\n", "i")
            file_url = f"https://{self.blob_service_client.account_name}.blob.core.windows.net/{self._container_name}/{sub_folder}{document.id}.pdf"
            return file_url

        except Exception as e:
            logger.log(f"Error uploading document: {e}\n", "e")
            raise

    def download(self, document: Document) -> Union[str, None]:
        """
        Download the document content from local storage.

        Args:
            document (Document): the Document record.

        Returns:
            Union[str, None]: Full local file path.
        """
        if os.getenv("ENVIRONMENT") == "development":
            tmp = "temporary"
            cwd = os.getcwd()
            tmppath = f"{cwd}/{tmp}"

            logger.log(f"Looking for document file:\n{document.id}.pdf:\n")
            filename = f"{document.id}.pdf"
            if not os.path.isdir(tmppath):
                os.mkdir(tmppath)
            if os.path.exists(f"{tmppath}/{filename}"):
                logger.log("Document found\n")
                return os.path.join(cwd, tmp, filename)

        logger.log(
            "Returning azure document file path stored into the container storage\n"
        )
        return document.file_url
