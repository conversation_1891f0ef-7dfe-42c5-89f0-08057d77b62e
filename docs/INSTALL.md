# Installation

## Table of contents

- [Installation](#installation)
  - [Table of contents](#table-of-contents)
  - [Configuration](#configuration)
    - [Poetry](#poetry)
    - [Secrets](#secrets)
    - [Database](#database)
    - [Pre-commit hook](#pre-commit-hook)
    - [Docker](#docker)
    - [Run application](#run-application)


## Configuration

1. Install poetry
2. Install dependencies
3. Run Flask migrations
4. Install PostgresSQL
5. Run application

### Poetry
Install with Homebrew or with Pyenv the same python version you'll find in the file pyproject.toml.
Then run the following command to install Poetry:

    Form mac users:
    curl -sSL https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py | python -

    poetry install


### Secrets
The `.env` file is used to store sensitive information, such as passwords and API keys.


### Database

The application coomunicate with a locally installed version of a PostgresSQL database. There isn't the need for a dockerized version since staging and production version will be communicating with an Azure database service. To install PostgresSQL on MacOS, you can use the following command:

    brew install postgresql@14

    brew services restart postgresql

Then say 'yes' to all the prompted questions.
To establish the connection with your local DB, in the `.env` file, change or add the following environment variables:

    SQLALCHEMY_DATABASE_URI

If you're running this application from the Spartax docker-compose project, you can stop here. The database and its user will be created automatically. If you're not, keep reading. Access the database from the shell by typing:

    psql postgres

Create the database:

    CREATE DATABASE spdb;

Create the user with an other password (differente from the previous one, which is your PostgresSQL installation root user password)

    CREATE USER spdbadmusr WITH PASSWORD '*an other password you'll be storing on .env*';

Grant all privileges to the newly created user for the database:

    GRANT ALL PRIVILEGES ON DATABASE spdb TO spdbadmusr;

Store your newly created user password in a untracked file called .env. Which will have also data supplier username and password.

If you'll keep using PostgresSQL from the shell here's a [useful cheatsheet](https://postgrescheatsheet.com/#/tables). Additionally, [PgAdmin](https://www.pgadmin.org/download/) is a good MacOS and Windows psql free client.

You're all set. The database tables will be created with the following Flask migrations commands.

    poetry run flask db upgrade

For user population run:

    poetry run flask seed run user

For coc population run, run the postman collection /coc POST with the default payload.

### Pre-commit hook
At first launch you should launch the pre-commit hook install command. This command will install the hook and bound it to every commit command you'll launch in the future:

    poetry run pre-commit install

### Docker
The app is setup to run on a multi-stage environment. Run

    cd .. && docker compose up

Or, if you're starting the appliaction only for development or test purposes, just run:

    docker run --name spartax-api -p 8888:8888 -ti api

And check 127.0.0.1:8888 in the browser.
To enter the container shell, run:

    docker exec -it spartax-api /bin/bash

or

    docker run --name spartax-api -p 8888:8888 -ti api bash

### Run application
In local/development environment always lunch the vscode debugger with one of the two configurations. Flask documentation tells us that the debugger is suitable for development purposes only. For staging and production, we'll use gunicorn.
