Additionally, you should start the API application only in debug mode in VsCode.

After the [installation](./INSTALL.md), you can run F5 to start the Flask or Gunicorn application debug configuration. The debug configuration in .vscode/launch.json named *Flask application* will start the Flask application in debug mode.

In the development environment, you shouldn't use poetry to run Flask (debug headaches) and it's better to limit its function solely to dependency management.

## Commit
The commit action is bound to a pre-commit hook. It's advisable to always commit through the shell and avoid the vscode Git extension cause it generates trouble in printing the pre-commit checks.
The pre-commit hook will run the following checks:
    check for added large files
    check python ast
    check docstring is first
    check for merge conflicts
    fix the end of files
    trim trailing whitespace
    import sorting

Once everything is ok, the commit will be performed. Probably it'll be helping make a pre-commit run in the shell before committing: some packages fix the errors and you need to re-stage them to be part of the commit.

### Temporary Folder for CoC Imports

When running the application in the development environment, a temporary folder is used to store JSON files of CoC import data. This is useful for testing and debugging purposes. The folder is located at api/app/models/coc.py:356:

    local_path = Path(f"/Users/<USER>/api/temporary/{container}/{folder}")

### Important Notes
- Ensure the `ENVIRONMENT` environment variable is set to `development` to enable this feature.
- The temporary folder and its contents are not used or needed in production environments.
- To enable local saving of CoC import data, add the following to your environment variables:

    export USER=<your-username>
