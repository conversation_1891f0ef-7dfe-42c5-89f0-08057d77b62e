{"name": "Spartax API", "dockerComposeFile": "../docker-compose.yml", "extensions": ["ms-azure-devops.azure-pipelines", "ms-azuretools.vscode-azureappservice", "ms-azuretools.vscode-azurestorage", "ms-azuretools.vscode-docker", "ms-vscode-remote.remote-containers", "ms-vscode.python", "wix.vscode-import-cost"], "remoteUser": "vscode", "service": "api", "settings": {"workspaceMount": "source=${localWorkspaceFolder},target=/,type=bind", "terminal.integrated.profiles.osx": {"zsh (login)": {"path": "zsh", "args": ["-l"], "env": {"TERM": "xterm-256color"}, "initialText": "Welcome to your new zsh shell!", "linuxShell": "zsh", "waitOnExit": true, "iconPath": "./resources/linux/terminal.png", "cwd": "${workspaceRoot}", "color": "terminal.ansiCyan"}}}, "workspaceFolder": "${localWorkspaceFolder}"}