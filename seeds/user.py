import os

from flask_seeder import Faker, Seeder
from sqlalchemy.exc import IntegrityError

from app.config import logger
from app.enums.user.resources import UserResources
from app.models.user import User


class UserSeeder(Seeder):
    """
    Class that handle the first DB seeding that follows the DB migration.

    - the DB is going to be populated with three users that have 'admin',
    'approver' and 'consumer' as role, respectively;
    """

    # run() will be called by <PERSON><PERSON><PERSON>-<PERSON><PERSON>
    def run(self):
        roles = UserResources.get_all_roles()
        users = []

        for role in roles:
            user_info = {
                "role": role,
                "id": os.getenv(f"DB_{role.upper()}_ID"),
                "email": os.getenv(f"{role.upper()}_EMAIL"),
                "name": os.getenv(f"{role.upper()}_NAME"),
                "surname": os.getenv(f"{role.upper()}_SURNAME"),
                "user_id": os.getenv(f"{role.upper()}_USERID"),
            }
            users.append(user_info)

        for user_info in users:
            user_data = {
                "id": user_info["id"],
                "email": user_info["email"],
                "employee_id": os.getenv("TEST_USERS_EMPLOYEE_ID"),
                "name": user_info["name"],
                "role": UserResources[user_info["role"].upper()].name.lower(),
                "surname": user_info["surname"],
                "residual_credits": UserResources[user_info["role"].upper()].value[
                    "residual_credits"
                ],
                "residual_requests": UserResources[user_info["role"].upper()].value[
                    "residual_requests"
                ],
                "user_id": user_info["user_id"],
            }

            user_faker = Faker(
                cls=User,
                init=user_data,
            )

            logger.log(f"{user_info['role'].upper()} USER INSTANTIATED\n", "i")
            for user in user_faker.create():
                logger.log(
                    f"CHECKING FOR EXISTING {user_info['role'].upper()} USER ({user}) INTO THE DATABASE...\n",
                    "i",
                )
                if (
                    self.db.session.query(User)
                    .filter(User.id == user_info["id"])
                    .one_or_none()
                    is None
                ):
                    logger.log(
                        f"{user_info['role'].upper()} USER DOES NOT EXIST: ATTEMPTING TO ADD IT...\n",
                        "i",
                    )
                    try:
                        self.db.session.add(user)
                        logger.log(f"{user_info['role'].upper()} USER ADDED\n", "i")
                    except IntegrityError as e:
                        logger.log(
                            f"ERROR RAISED (MAY NOT CAUSING ANY APP FAILURE):\n{e}\n",
                            "w",
                        )
                        pass

        self.db.session.flush()
        self.db.session.commit()
        self.db.session.expunge_all()
