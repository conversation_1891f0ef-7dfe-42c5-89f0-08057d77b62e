from __future__ import with_statement

import logging
import os
import sys
from configparser import ConfigParser
from logging.config import fileConfig

from alembic import context
from sqlalchemy import MetaData, create_engine, pool

if os.getenv("WEBSITE_SITE_NAME") is None:
    try:
        from dotenv import load_dotenv

        load_dotenv()
    except ImportError:
        pass

config = context.config

# Disable interpolation to prevent '%' parsing issues
if config.file_config:
    config.file_config._interpolation = None

# Set up logging from config
fileConfig(config.config_file_name)
logger = logging.getLogger("alembic.env")

# Get DB URL from environment
database_url = os.getenv("SQLALCHEMY_DATABASE_URI")
if not database_url:
    raise RuntimeError("Environment variable SQLALCHEMY_DATABASE_URI not set")

# Disable interpolation for safe DB URL usage
# config.set_main_option("sqlalchemy.url", str(database_url))

# Add project path to sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))


def get_app():
    from app.main import app

    return app


def get_metadata():
    try:
        app = get_app()
        with app.app_context():
            return app.extensions["migrate"].db.metadata
    except Exception:
        logger.warning("Running outside Flask context. Using reflection for metadata.")
        engine = create_engine(database_url)
        metadata = MetaData()
        metadata.reflect(bind=engine)
        return metadata


# Load target metadata
target_metadata = get_metadata()


def run_migrations_offline():
    """Run migrations in 'offline' mode."""
    context.configure(
        url=database_url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )
    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode."""

    def process_revision_directives(context, revision, directives):
        if getattr(config.cmd_opts, "autogenerate", False):
            script = directives[0]
            if script.upgrade_ops.is_empty():
                directives[:] = []
                logger.info("No changes in schema detected.")

    connectable = create_engine(database_url, poolclass=pool.NullPool)

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            process_revision_directives=process_revision_directives,
            # Inject the URL directly (safe from interpolation)
            url=database_url,
        )
        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
