"""Testing jsonb array for coc.changes 2

Revision ID: b1895e289273
Revises: 1ed1ae3a7415
Create Date: 2024-07-29 07:07:48.033942

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b1895e289273'
down_revision = '1ed1ae3a7415'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('changes', postgresql.ARRAY(postgresql.JSONB(astext_type=sa.Text())), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'changes')
    # ### end Alembic commands ###
