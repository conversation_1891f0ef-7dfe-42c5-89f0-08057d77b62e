"""empty message

Revision ID: ec29ddb89bfc
Revises: 9a04ec04ef47
Create Date: 2024-02-06 19:35:25.653410

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ec29ddb89bfc'
down_revision = '9a04ec04ef47'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('request', 'requested_at',
               existing_type=postgresql.UUID(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('request', 'requested_at',
               existing_type=postgresql.UUID(),
               nullable=False)
    # ### end Alembic commands ###
