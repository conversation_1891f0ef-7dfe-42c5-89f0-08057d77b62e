"""Enlarged request.bought_by column size

Revision ID: e2d288fe9d90
Revises: 89a23b96e509
Create Date: 2024-08-29 09:51:26.118667

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'e2d288fe9d90'
down_revision = '89a23b96e509'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('request', 'bought_by',
               existing_type=sa.VARCHAR(length=12),
               type_=sa.String(length=25),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('request', 'bought_by',
               existing_type=sa.String(length=25),
               type_=sa.VARCHAR(length=12),
               existing_nullable=True)
    # ### end Alembic commands ###
