"""empty message

Revision ID: d4840cca1d22
Revises: ae95767f7bd7
Create Date: 2024-07-26 13:12:45.125098

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d4840cca1d22"
down_revision = "ae95767f7bd7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_foreign_key(None, 'coc', 'user', ['engagement_partner_id'], ['id'])
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "coc", type_="foreignkey")
    # ### end Alembic commands ###
