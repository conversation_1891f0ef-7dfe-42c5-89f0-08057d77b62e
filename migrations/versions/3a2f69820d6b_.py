"""empty message

Revision ID: 3a2f69820d6b
Revises: 92897f8b9eef
Create Date: 2024-08-12 17:11:31.038217

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '3a2f69820d6b'
down_revision = '92897f8b9eef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coc', 'engagement_partner',
               existing_type=sa.VARCHAR(length=25),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('coc', 'parnr',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('coc', 'profit_center',
               existing_type=sa.VARCHAR(length=15),
               type_=sa.String(length=255),
               existing_nullable=True)
    op.alter_column('coc', 'service_line_code',
               existing_type=sa.VARCHAR(length=15),
               type_=sa.String(length=255),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coc', 'service_line_code',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=15),
               existing_nullable=True)
    op.alter_column('coc', 'profit_center',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=15),
               existing_nullable=True)
    op.alter_column('coc', 'parnr',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('coc', 'engagement_partner',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=25),
               existing_nullable=False)
    # ### end Alembic commands ###
