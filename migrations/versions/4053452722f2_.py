"""empty message

Revision ID: 4053452722f2
Revises: b2c8dd6f6033
Create Date: 2024-07-26 18:09:36.116773

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '4053452722f2'
down_revision = 'b2c8dd6f6033'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'closed')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('closed', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
