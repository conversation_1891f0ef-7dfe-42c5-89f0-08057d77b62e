"""Removed user.approver_id column

Revision ID: 89a23b96e509
Revises: dcb789faf437
Create Date: 2024-08-25 08:44:23.942405

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '89a23b96e509'
down_revision = 'dcb789faf437'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'approver_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('approver_id', sa.VARCHAR(length=12), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
