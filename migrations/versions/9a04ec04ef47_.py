"""empty message

Revision ID: 9a04ec04ef47
Revises:
Create Date: 2024-02-06 19:22:08.699041

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "9a04ec04ef47"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():

    # Check if the 'UserStatusEnum' already exists before creating
    # existing_types = sa.inspect(op.get_bind()).get_enums()
    # if "UserStatusEnum" not in [enum["name"] for enum in existing_types]:
    #     op.execute(
    #         "CREATE TYPE \"UserStatusEnum\" AS ENUM('draft', 'pending', 'accepted', 'rejected')"
    #     )

    # # Check if the 'RequestActionEnum' already exists before creating
    # if "RequestActionEnum" not in [enum["name"] for enum in existing_types]:
    #     op.execute(
    #         "CREATE TYPE \"RequestActionEnum\" AS ENUM('buy', 'rebuy', 'assign')"
    #     )

    op.create_table(
        "company",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("vat_code", sa.String(length=11), nullable=True),
        sa.Column("tax_code", sa.String(length=16), nullable=True),
        sa.Column("ranking", sa.Float(), nullable=True),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("crif_number", sa.String(length=255), nullable=False),
        sa.Column("activity_description", sa.Text(), nullable=False),
        sa.Column("address", sa.String(length=255), nullable=False),
        sa.Column("town", sa.String(length=255), nullable=False),
        sa.Column("village", sa.String(length=255), nullable=True),
        sa.Column("zip_code", sa.String(length=255), nullable=True),
        sa.Column("province_code", sa.String(length=255), nullable=True),
        sa.Column("ateco_code", sa.String(length=255), nullable=True),
        sa.Column("ateco_description", sa.String(length=255), nullable=True),
        sa.Column("province_description", sa.String(length=255), nullable=True),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.Column("region", sa.String(length=200), nullable=True),
        sa.Column("cciaa", sa.String(length=255), nullable=True),
        sa.Column("rea", sa.String(length=255), nullable=True),
        sa.Column("legal_form_code", sa.String(length=255), nullable=True),
        sa.Column("legal_form_description", sa.String(length=255), nullable=True),
        sa.Column("unit_type_code", sa.String(length=255), nullable=True),
        sa.Column("unit_type_description", sa.String(length=255), nullable=True),
        sa.Column("company_status_code", sa.String(length=255), nullable=True),
        sa.Column("company_status_description", sa.String(length=255), nullable=True),
        sa.Column("activity_status_code", sa.String(length=255), nullable=True),
        sa.Column(
            "activity_status_code_description", sa.String(length=255), nullable=True
        ),
        sa.Column("flag_out_of_business", sa.Boolean(), nullable=True),
        sa.Column("flag_news_available", sa.Boolean(), nullable=True),
        sa.Column("flag_payment_info_available", sa.Boolean(), nullable=True),
        sa.Column("last_balance_date", sa.DateTime(), nullable=True),
        sa.Column("flag_belong_to_a_group", sa.Boolean(), nullable=True),
        sa.Column("flag_pa", sa.String(length=3), nullable=True),
        sa.Column("website", sa.String(length=255), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_company_tax_code"), "company", ["tax_code"], unique=True)
    op.create_index(op.f("ix_company_vat_code"), "company", ["vat_code"], unique=True)
    op.create_table(
        "person",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("tax_code", sa.String(length=16), nullable=True),
        sa.Column("ranking", sa.Float(), nullable=True),
        sa.Column("surname", sa.String(length=55), nullable=True),
        sa.Column("name", sa.String(length=55), nullable=True),
        sa.Column("birth_date", sa.DateTime(), nullable=True),
        sa.Column("birth_town", sa.String(length=55), nullable=True),
        sa.Column("birth_province", sa.String(length=55), nullable=True),
        sa.Column("birth_province_description", sa.String(length=55), nullable=True),
        sa.Column("gender", sa.String(length=55), nullable=True),
        sa.Column("address", sa.String(length=55), nullable=True),
        sa.Column("town", sa.String(length=55), nullable=True),
        sa.Column("village", sa.String(length=55), nullable=True),
        sa.Column("province", sa.String(length=55), nullable=True),
        sa.Column("zip", sa.String(length=55), nullable=True),
        sa.Column("country", sa.String(length=55), nullable=True),
        sa.Column("has_roles", sa.String(length=55), nullable=True),
        sa.Column("is_sole_trader", sa.String(length=55), nullable=True),
        sa.Column("is_shareholder", sa.String(length=55), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_person_tax_code"), "person", ["tax_code"], unique=True)
    op.create_table(
        "user",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("azure_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("email", sa.String(length=55), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=False),
        sa.Column("role", sa.String(length=9), nullable=False),
        sa.Column("surname", sa.String(length=128), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("last_access", sa.DateTime(), nullable=True),
        sa.Column("residual_credits", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column(
            "residual_requests", sa.Numeric(precision=10, scale=2), nullable=True
        ),
        sa.Column("user_id", sa.String(length=25), autoincrement=False, nullable=False),
        sa.Column(
            "approver_id", sa.String(length=12), autoincrement=False, nullable=True
        ),
        sa.Column("ytd_approved_cost", sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("user_id"),
    )
    op.create_table(
        "coc",
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.Column("code", sa.String(length=255), nullable=False),
        sa.Column("client", sa.String(length=255), nullable=False),
        sa.Column("deactivated_date", sa.DateTime(), nullable=True),
        sa.Column("engagement_partner", sa.String(length=25), nullable=False),
        sa.Column(
            "engagement_partner_id", postgresql.UUID(as_uuid=True), nullable=True
        ),
        sa.Column("engagement_partner_email", sa.String(length=25), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("imported_date", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["engagement_partner_email"],
            ["user.email"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "document",
        sa.Column("code_atto", sa.String(length=255), nullable=True),
        sa.Column("company_code", sa.String(length=255), nullable=True),
        sa.Column("file_url", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("data_chiusura_esercizio", sa.String(length=255), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("num_pag_bilancio", sa.String(length=255), nullable=True),
        sa.Column("tax_code", sa.String(length=16), nullable=True),
        sa.Column("ref_id", sa.String(length=255), nullable=True),
        sa.Column("type", sa.String(length=255), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("year", sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(
            ["company_code"],
            ["company.vat_code"],
        ),
        sa.ForeignKeyConstraint(
            ["tax_code"],
            ["person.tax_code"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "request",
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("document_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("request_timestamp", sa.DateTime(), nullable=True),
        sa.Column("response_timestamp", sa.DateTime(), nullable=True),
        sa.Column("result_code", sa.String(length=55), nullable=True),
        sa.Column("result_severity", sa.String(length=55), nullable=True),
        sa.Column("purchase", sa.Boolean(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("user_id", sa.String(length=25), nullable=True),
        sa.Column(
            "status",
            sa.Enum("draft", "pending", "accepted", "rejected", name="UserStatusEnum"),
            server_default="draft",
            nullable=False,
        ),
        sa.Column("coc", sa.String(length=55), nullable=True),
        sa.Column("requested_at", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("bought_by", sa.String(length=12), nullable=True),
        sa.Column(
            "service_request_info",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
        sa.Column("company_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("person_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column(
            "action",
            sa.Enum("buy", "rebuy", "assign", name="RequestActionEnum"),
            server_default="buy",
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["company_id"],
            ["company.id"],
        ),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["document.id"],
        ),
        sa.ForeignKeyConstraint(
            ["person_id"],
            ["person.id"],
        ),
        sa.ForeignKeyConstraint(
            ["requested_at"],
            ["user.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.user_id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("request")
    op.drop_table("document")
    op.drop_table("coc")
    op.drop_table("user")
    op.drop_index(op.f("ix_person_tax_code"), table_name="person")
    op.drop_table("person")
    op.drop_index(op.f("ix_company_vat_code"), table_name="company")
    op.drop_index(op.f("ix_company_tax_code"), table_name="company")
    op.drop_table("company")
    # ### end Alembic commands ###
