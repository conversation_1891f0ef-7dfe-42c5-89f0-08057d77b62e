"""Coc.closed now it's a boolean

Revision ID: 6232cd0f2814
Revises: 954f7ea50bb6
Create Date: 2024-07-25 11:16:29.472075

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '6232cd0f2814'
down_revision = '954f7ea50bb6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coc', 'closed',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coc', 'closed',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
