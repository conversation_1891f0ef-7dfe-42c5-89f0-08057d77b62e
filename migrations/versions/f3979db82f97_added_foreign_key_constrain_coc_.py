"""Added foreign key constrain coc.engagement_partner_id and user.id

Revision ID: f3979db82f97
Revises: 9bd8a6c82953
Create Date: 2024-07-26 13:44:36.567842

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'f3979db82f97'
down_revision = '9bd8a6c82953'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'coc', 'user', ['engagement_partner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'coc', type_='foreignkey')
    # ### end Alembic commands ###
