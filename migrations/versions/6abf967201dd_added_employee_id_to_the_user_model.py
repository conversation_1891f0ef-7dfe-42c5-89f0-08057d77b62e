import random

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision = "6abf967201dd"
down_revision = "6232cd0f2814"
branch_labels = None
depends_on = None


def upgrade():
    # Step 1: Add the column as nullable initially
    op.add_column("user", sa.Column("employee_id", sa.String(length=10), nullable=True))

    # Step 2: Populate the column with random 5-digit numbers for existing rows
    connection = op.get_bind()
    users = connection.execute(
        text('SELECT id FROM "user" WHERE employee_id IS NULL')
    ).fetchall()

    for user in users:
        random_employee_id = f"{random.randint(10000, 99999)}"
        connection.execute(
            f'UPDATE "user" SET employee_id = \'{random_employee_id}\' WHERE id = \'{user["id"]}\''
        )

    # Step 3: Alter the column to be non-nullable
    op.alter_column("user", "employee_id", nullable=False)

    # Step 4: Add the unique constraint
    # op.create_unique_constraint("uq_user_employee_id", "user", ["employee_id"])


def downgrade():
    # Step 1: Drop the unique constraint
    op.drop_constraint("uq_user_employee_id", "user", type_="unique")

    # Step 2: Drop the column
    op.drop_column("user", "employee_id")
