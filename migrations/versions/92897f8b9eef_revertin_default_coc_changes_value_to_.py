"""Revertin default coc.changes value to empty list 3

Revision ID: 92897f8b9eef
Revises: 4b47d4da35c5
Create Date: 2024-07-29 07:40:59.547974

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '92897f8b9eef'
down_revision = '4b47d4da35c5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('changes', postgresql.JSONB(astext_type=sa.Text()), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'changes')
    # ### end Alembic commands ###
