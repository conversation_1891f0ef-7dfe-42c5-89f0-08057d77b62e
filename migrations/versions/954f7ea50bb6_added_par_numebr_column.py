"""Added par_numebr column

Revision ID: 954f7ea50bb6
Revises: 83ddc2d76f1c
Create Date: 2024-07-25 10:19:39.265718

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '954f7ea50bb6'
down_revision = '83ddc2d76f1c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('par_number', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'par_number')
    # ### end Alembic commands ###
