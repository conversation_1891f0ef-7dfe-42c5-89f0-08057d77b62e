"""Removed active boolean column

Revision ID: ee909632f5d7
Revises: ea3e69f4ec42
Create Date: 2024-07-26 18:29:41.300360

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'ee909632f5d7'
down_revision = 'ea3e69f4ec42'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'active')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('active', sa.BOOLEAN(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
