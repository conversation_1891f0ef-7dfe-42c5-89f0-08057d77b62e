"""coc.closed back to boolean type

Revision ID: b2c8dd6f6033
Revises: f3979db82f97
Create Date: 2024-07-26 18:07:25.128356

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b2c8dd6f6033"
down_revision = "f3979db82f97"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "coc", "closed", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "coc", "closed", existing_type=sa.VARCHAR(length=255), nullable=False
    )
    # ### end Alembic commands ###
