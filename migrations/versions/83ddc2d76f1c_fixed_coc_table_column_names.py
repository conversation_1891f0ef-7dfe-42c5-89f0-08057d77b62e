"""Fixed coc table column names

Revision ID: 83ddc2d76f1c
Revises: c0b77a79b9b5
Create Date: 2024-07-25 10:17:54.533876

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '83ddc2d76f1c'
down_revision = 'c0b77a79b9b5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('description', sa.String(length=255), nullable=True))
    op.drop_column('coc', 'wbs_description')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('wbs_description', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.drop_column('coc', 'description')
    # ### end Alembic commands ###
