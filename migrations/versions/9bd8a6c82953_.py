"""empty message

Revision ID: 9bd8a6c82953
Revises: d4840cca1d22
Create Date: 2024-07-26 13:43:00.264592

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9bd8a6c82953'
down_revision = 'd4840cca1d22'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('engagement_partner_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.alter_column('user', 'employee_id',
               existing_type=sa.VARCHAR(length=10),
               nullable=True)
    op.create_unique_constraint(None, 'user', ['employee_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='unique')
    op.alter_column('user', 'employee_id',
               existing_type=sa.VARCHAR(length=10),
               nullable=False)
    op.drop_column('coc', 'engagement_partner_id')
    # ### end Alembic commands ###
