"""Changed coc.engagement_partner_id foreign key from user.user_id to user.id

Revision ID: ae95767f7bd7
Revises: 0c35de33a413
Create Date: 2024-07-26 11:42:47.543961

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ae95767f7bd7"
down_revision = "0c35de33a413"
branch_labels = None
depends_on = None


def upgrade():
    ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("coc", "engagement_partner_id")


# ### end Alembic commands ###


def downgrade():
    pass


#     # ### end Alembic commands ###
