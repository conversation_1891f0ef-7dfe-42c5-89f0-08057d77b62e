"""Revertin default coc.changes value to empty list

Revision ID: 4b47d4da35c5
Revises: b1895e289273
Create Date: 2024-07-29 07:33:07.672221

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4b47d4da35c5'
down_revision = 'b1895e289273'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'changes')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('changes', postgresql.ARRAY(postgresql.JSONB(astext_type=sa.Text())), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
