"""empty message

Revision ID: ea3e69f4ec42
Revises: 4053452722f2
Create Date: 2024-07-26 18:09:54.757905

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'ea3e69f4ec42'
down_revision = '4053452722f2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('closed', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'closed')
    # ### end Alembic commands ###
