"""Testing jsonb array for coc.changes

Revision ID: 1ed1ae3a7415
Revises: 1fa0df0355c9
Create Date: 2024-07-29 07:07:07.658365

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1ed1ae3a7415'
down_revision = '1fa0df0355c9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coc', 'changes')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('changes', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
