"""empty message

Revision ID: 0c35de33a413
Revises: 6abf967201dd
Create Date: 2024-07-26 11:23:22.900555

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0c35de33a413"
down_revision = "6abf967201dd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column(
    #     "user", "employee_id", existing_type=sa.VARCHAR(length=10), nullable=True
    # )
    op.alter_column("user", "email", existing_type=sa.VARCHAR(length=55), nullable=True)
    op.alter_column("user", "name", existing_type=sa.VARCHAR(length=128), nullable=True)
    op.alter_column(
        "user", "surname", existing_type=sa.VARCHAR(length=128), nullable=True
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "user", "surname", existing_type=sa.VARCHAR(length=128), nullable=False
    )
    op.alter_column(
        "user", "name", existing_type=sa.VARCHAR(length=128), nullable=False
    )
    op.alter_column(
        "user", "email", existing_type=sa.VARCHAR(length=55), nullable=False
    )
    op.alter_column(
        "user", "employee_id", existing_type=sa.VARCHAR(length=10), nullable=False
    )
    # ### end Alembic commands ###
