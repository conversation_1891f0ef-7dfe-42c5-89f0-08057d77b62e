"""Added pushlog model

Revision ID: fced5c659ee5
Revises: e2d288fe9d90
Create Date: 2025-01-14 13:44:49.141291

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fced5c659ee5'
down_revision = 'e2d288fe9d90'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('push_log',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('completed_at', sa.DateTime(), nullable=False),
    sa.Column('pushed_at', sa.DateTime(), nullable=False),
    sa.Column('received_records', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('push_log')
    # ### end Alembic commands ###
