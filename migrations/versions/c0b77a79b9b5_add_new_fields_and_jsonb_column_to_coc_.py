"""Add new fields and JSONB column to Coc model

Revision ID: c0b77a79b9b5
Revises: b9e704d02f77
Create Date: 2024-07-25 10:11:46.064339

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c0b77a79b9b5'
down_revision = 'b9e704d02f77'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('engagement_partner_email', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('wbs_description', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('closed', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('is_expenses_blocked', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('participating_partner_name', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('participating_partner', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('last_edit_date', sa.DateTime(), nullable=True))
    op.add_column('coc', sa.Column('changes', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('coc', 'profit_center',
               existing_type=sa.VARCHAR(length=15),
               nullable=True)
    op.alter_column('coc', 'service_line_code',
               existing_type=sa.VARCHAR(length=15),
               nullable=True)
    op.create_foreign_key(None, 'coc', 'user', ['engagement_partner_email'], ['email'])
    op.drop_column('coc', 'partecipating_partner')
    op.drop_column('coc', 'description')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('coc', sa.Column('partecipating_partner', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'coc', type_='foreignkey')
    op.alter_column('coc', 'service_line_code',
               existing_type=sa.VARCHAR(length=15),
               nullable=False)
    op.alter_column('coc', 'profit_center',
               existing_type=sa.VARCHAR(length=15),
               nullable=False)
    op.drop_column('coc', 'changes')
    op.drop_column('coc', 'last_edit_date')
    op.drop_column('coc', 'participating_partner')
    op.drop_column('coc', 'participating_partner_name')
    op.drop_column('coc', 'is_expenses_blocked')
    op.drop_column('coc', 'closed')
    op.drop_column('coc', 'wbs_description')
    op.drop_column('coc', 'engagement_partner_email')
    # ### end Alembic commands ###
