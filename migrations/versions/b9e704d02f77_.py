"""empty message

Revision ID: b9e704d02f77
Revises: ec29ddb89bfc
Create Date: 2024-05-24 22:06:43.490527

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'b9e704d02f77'
down_revision = 'ec29ddb89bfc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('description', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('partecipating_partner', sa.String(length=255), nullable=True))
    op.add_column('coc', sa.Column('profit_center', sa.String(length=15), nullable=False))
    op.add_column('coc', sa.Column('service_line_code', sa.String(length=15), nullable=False))
    op.drop_constraint('coc_engagement_partner_email_fkey', 'coc', type_='foreignkey')
    op.drop_column('coc', 'engagement_partner_email')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('engagement_partner_email', sa.VARCHAR(length=25), autoincrement=False, nullable=True))
    op.create_foreign_key('coc_engagement_partner_email_fkey', 'coc', 'user', ['engagement_partner_email'], ['email'])
    op.drop_column('coc', 'service_line_code')
    op.drop_column('coc', 'profit_center')
    op.drop_column('coc', 'partecipating_partner')
    op.drop_column('coc', 'description')
    # ### end Alembic commands ###
