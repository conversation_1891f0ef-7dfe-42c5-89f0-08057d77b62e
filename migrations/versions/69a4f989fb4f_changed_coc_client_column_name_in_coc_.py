"""Changed coc.client column name in coc_company_customer_name

Revision ID: 69a4f989fb4f
Revises: ee909632f5d7
Create Date: 2024-07-26 18:38:58.120141

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '69a4f989fb4f'
down_revision = 'ee909632f5d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('company_customer_name', sa.String(length=255), nullable=False))
    op.drop_column('coc', 'client')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('client', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.drop_column('coc', 'company_customer_name')
    # ### end Alembic commands ###
