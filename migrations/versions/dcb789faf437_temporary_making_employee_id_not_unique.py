"""Temporary making employee_id not unique

Revision ID: dcb789faf437
Revises: 3a2f69820d6b
Create Date: 2024-08-25 08:39:03.027236

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'dcb789faf437'
down_revision = '3a2f69820d6b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_employee_id_key', 'user', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('user_employee_id_key', 'user', ['employee_id'])
    # ### end Alembic commands ###
