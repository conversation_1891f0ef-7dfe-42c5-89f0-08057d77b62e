"""Parn_nr to parnr

Revision ID: 1fa0df0355c9
Revises: 69a4f989fb4f
Create Date: 2024-07-27 15:00:26.916785

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '1fa0df0355c9'
down_revision = '69a4f989fb4f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('parnr', sa.String(length=255), nullable=True))
    op.drop_column('coc', 'par_number')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coc', sa.Column('par_number', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.drop_column('coc', 'parnr')
    # ### end Alembic commands ###
