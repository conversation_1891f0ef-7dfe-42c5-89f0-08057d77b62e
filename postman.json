{"info": {"_postman_id": "18adc484-6ea4-46d4-998b-da1cc383d61c", "name": "[dev] spartax", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "815675"}, "item": [{"name": "auth", "item": [{"name": "access_token", "event": [{"listen": "test", "script": {"exec": ["pm.test(pm.info.requestName, () => {", "    pm.response.to.not.be.error;", "    pm.response.to.not.have.jsonBody('error');", "});", "pm.globals.set(\"bearerToken\", pm.response.json().access_token);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{clientId}}", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}, {"key": "scopes", "value": "[\"Directory.AccessAsUser.All\",\"Directory.Read.All\",\"email\",\"Mail.Read\",\"offline_access\",\"openid\",\"profile\",\"User.Read.All\",\"User.Read\",\"User.ReadBasic.All\"]", "type": "text"}]}, "url": {"raw": "https://login.microsoftonline.com/{{tenantId}}/oauth2/token", "protocol": "https", "host": ["login", "microsoftonline", "com"], "path": ["{{tenantId}}", "oauth2", "token"]}}, "response": []}, {"name": "openid conf", "event": [{"listen": "test", "script": {"exec": ["pm.test(pm.info.requestName, () => {", "    pm.response.to.not.be.error;", "    pm.response.to.not.have.jsonBody('error');", "});", "pm.globals.set(\"bearerToken\", pm.response.json().access_token);var template = `", "<style type=\"text/css\">", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}", "    .tftable tr {background-color:#ffffff;}", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}", "    .tftable tr:hover {background-color:#e0ffff;}", "</style>", "", "<table class=\"tftable\" border=\"1\">", "    <tr>", "        <th>Property</th>", "        <th>Value</th>", "    </tr>", "    <tr>", "        <td>Token Endpoint</td>", "        <td>{{response.token_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>Supported Token Endpoint Auth Methods</td>", "        <td>{{response.token_endpoint_auth_methods_supported}}</td>", "    </tr>", "    <tr>", "        <td>JWKS URI</td>", "        <td>{{response.jwks_uri}}</td>", "    </tr>", "    <tr>", "        <td>Response Modes Supported</td>", "        <td>{{response.response_modes_supported}}</td>", "    </tr>", "    <tr>", "        <td>Subject Types Supported</td>", "        <td>{{response.subject_types_supported}}</td>", "    </tr>", "    <tr>", "        <td>ID Token Signing Algorithms Supported</td>", "        <td>{{response.id_token_signing_alg_values_supported}}</td>", "    </tr>", "    <tr>", "        <td>Response Types Supported</td>", "        <td>{{response.response_types_supported}}</td>", "    </tr>", "    <tr>", "        <td>Scopes Supported</td>", "        <td>{{response.scopes_supported}}</td>", "    </tr>", "    <tr>", "        <td>Issuer</td>", "        <td>{{response.issuer}}</td>", "    </tr>", "    <tr>", "        <td>Microsoft Multi-Refresh Token</td>", "        <td>{{response.microsoft_multi_refresh_token}}</td>", "    </tr>", "    <tr>", "        <td>Authorization Endpoint</td>", "        <td>{{response.authorization_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>Device Authorization Endpoint</td>", "        <td>{{response.device_authorization_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>HTTP Logout Supported</td>", "        <td>{{response.http_logout_supported}}</td>", "    </tr>", "    <tr>", "        <td>Frontchannel Logout Supported</td>", "        <td>{{response.frontchannel_logout_supported}}</td>", "    </tr>", "    <tr>", "        <td>End Session Endpoint</td>", "        <td>{{response.end_session_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>Claims Supported</td>", "        <td>{{response.claims_supported}}</td>", "    </tr>", "    <tr>", "        <td>Check Session Iframe</td>", "        <td>{{response.check_session_iframe}}</td>", "    </tr>", "    <tr>", "        <td>Userinfo Endpoint</td>", "        <td>{{response.userinfo_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>Kerberos Endpoint</td>", "        <td>{{response.kerberos_endpoint}}</td>", "    </tr>", "    <tr>", "        <td>Tenant Region Scope</td>", "        <td>{{response.tenant_region_scope}}</td>", "    </tr>", "    <tr>", "        <td>Cloud Instance Name</td>", "        <td>{{response.cloud_instance_name}}</td>", "    </tr>", "    <tr>", "        <td>Cloud Graph Host Name</td>", "        <td>{{response.cloud_graph_host_name}}</td>", "    </tr>", "    <tr>", "        <td>MSGraph Host</td>", "        <td>{{response.msgraph_host}}</td>", "    </tr>", "    <tr>", "        <td>RBAC URL</td>", "        <td>{{response.rbac_url}}</td>", "    </tr>", "</table>", "`;", "", "function constructVisualizerPayload() {", "    return { response: pm.response.json() }", "}", "", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "https://login.microsoftonline.com/{{tenantId}}/.well-known/openid-configuration", "protocol": "https", "host": ["login", "microsoftonline", "com"], "path": ["{{tenantId}}", ".well-known", "openid-configuration"]}}, "response": []}, {"name": "keys", "event": [{"listen": "test", "script": {"exec": ["pm.test(pm.info.requestName, () => {", "    pm.response.to.not.be.error;", "    pm.response.to.not.have.jsonBody('error');", "});", "pm.globals.set(\"bearerToken\", pm.response.json().access_token);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "https://login.microsoftonline.com/{{tenantId}}/discovery/keys?appid={{clientId}}", "protocol": "https", "host": ["login", "microsoftonline", "com"], "path": ["{{tenantId}}", "discovery", "keys"], "query": [{"key": "appid", "value": "{{clientId}}"}]}}, "response": []}, {"name": "profile", "event": [{"listen": "test", "script": {"exec": ["pm.test(pm.info.requestName, () => {", "    pm.response.to.not.be.error;", "    pm.response.to.not.have.jsonBody('error');", "});", "pm.globals.set(\"bearerToken\", pm.response.json().access_token);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "https://graph.microsoft.com/v1.0/me", "protocol": "https", "host": ["graph", "microsoft", "com"], "path": ["v1.0", "me"], "query": [{"key": "", "value": "", "disabled": true}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IktRMnRBY3JFN2xCYVZWR0JtYzVGb2JnZEpvNCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HL2APO5CVE5uExZ0NNPECKWZVePwJJamlojuVUdes8fEFbiCkluWSgPAS9V9nh31fFuEB9bZ9RH26iB3apSPhxpwQIuZDrVFYC8roLdQ3UIzypvL6a_GAM7-zrE0YYyDgEFQaWuANwPiKGpBY82_0jpJQFnH5fXbNiWhCaNhBrolNgZc1k41GXW1avsC_wtUFaXqgifaf1eNnjGxADYdQca4nCA9zzUh2UXPR-gZuv99vmZWcRVmVRtX6F2dRb1Lo0gO5rSwEIG7yhfsYn9Fv747yxBrieOG0X6VjiEemqdzM-yzrHNc7OhZ51zktVrNDoUaFnR2-gLnRj3AwhaPGg", "type": "string"}]}, "method": "GET", "header": [{"key": "Id", "value": "<EMAIL>", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/user", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["user"]}}, "response": []}, {"name": "Mail", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [{"key": "Id", "value": "consumer", "type": "text"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJub25jZSI6InBZb2hzT3pzdnZRSFNQZVk4QTNSLUhHNzgxbV84Q2plbU5uSUJ4X0dXd0UiLCJhbGciOiJSUzI1NiIsIng1dCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fBOfb8r--VQio_S1R6RX9MKUJTWonGbVmHzAf2JcxRUso_aXgZqcQ_N4unNZXFQtQLn5LekXr6yFyipwJ_bZ3mi-z6J-K3tUlLj6r070flgy-8gTyzkSZCHyUUZUDteTdf10cqZs-nvqlo_0kMvDtlndkBVcXHpZLnTBDL1cNA3gZinVoJ_HmOBrRvrmVYZLhzi0DJxcs40C3we9rMU1uUN8XTJ6bbiGzT5rulD7XZltUyNV6lrGUgP48-0b2cJu5tj3XnSH07BI8SCnm-xQsmTZXJDmByQykdbpwXd6MbAKHMYl4GsZKuL2gS9cQDg-NdGHoL-hvswgR-IRzSbang", "type": "text"}], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"subject\": \"Oggetto mail\", \"message\": \"Corpo del messaggio\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8888/mail", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["mail"]}}, "response": []}, {"name": "All Consumer Docs (purchased)", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/documents", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["documents"]}}, "response": []}, {"name": "All Approver Docs (purchased)", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/documents", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["documents"]}}, "response": []}, {"name": "Single Consumer Doc (purchased)", "request": {"method": "GET", "header": [{"key": "Id", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q76lghzkM7AOs0_OuwRvdw90j5FDHbAJpetXVJP6Ntw1z1wQadL6nMP1BmkFjyx8t_dDwh28voFxthNL3I3qMoVLTzy7PBYqHvhJwCWvDpa_vSj7STO4poleDE7olkEM4DMnSyY5Ii0TC3QUb0msVoPK0MN5pw_p59GD23HMV6R2vIHBIU6k5qb6kEy_EprtRt2xDJzgq4MMhsPqs0ZfWwoXdfHzejOE7cE5l47Ti0maI9BMSeTOP8G7Ly2RmkCZ5Aywbbvbuc0DTUKCGoy9qzQj7Nfai7U0PnAElD5iT9Mfvl_7bstHrGw0I3Crc1cpHXrSX2uXmHZQ9RTR8RRQMw", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/document/:document_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["document", ":document_id"], "variable": [{"key": "document_id", "value": "32a8cb63-0ddf-4742-b862-d1aca255a021"}]}}, "response": []}, {"name": "Company", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/companies/:vat_code", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["companies", ":vat_code"], "variable": [{"key": "vat_code", "value": "02334810302"}]}}, "response": []}, {"name": "Coc", "request": {"method": "GET", "header": [{"key": "Id", "value": "<PERSON><PERSON><PERSON>", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/coc", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["coc"]}}, "response": []}, {"name": "Coc", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Id", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "[\n    {\n        \"WbsCode\": \"TIM00181-01-04-01-1000\",\n        \"WbsDescritption\": \"12-Data and Analytics Modernizatidd\",\n        \"CompanyCustomerName\": \"TIM SNCC\",\n        \"ProfitCenter\": \"0222222210\",\n        \"EngagementPartner\": \"<PERSON>ai<PERSON>\",\n        \"Closed\": \"\",\n        \"IsExpensesBlocked\": \"\",\n        \"ServiceLineCode\": \"038044468233\",\n        \"PARNR\": \"06001472\",\n        \"PartecipatingPartner (nome e cognome del partecipating partner)\": \"ciao ciao\",\n        \"PARN PartecipatingPartner\": \"amma sdjdsj\"\n    },\n    {\n        \"WbsCode\": \"TIM00181-01-0C-01-1111\",\n        \"WbsDescritption\": \"2-Data and Analytics Modernizati-\",\n        \"CompanyCustomerName\": \"NIKE ITALY SPA\",\n        \"ProfitCenter\": \"0380444601\",\n        \"EngagementPartner\": \"<PERSON>ai<PERSON>\",\n        \"Closed\": \"\",\n        \"IsExpensesBlocked\": \"\",\n        \"ServiceLineCode\": \"0380444601\",\n        \"PARNR\": \"*********\",\n        \"PartecipatingPartner (nome e cognome del partecipating partner)\": null,\n        \"PARN PartecipatingPartner\": null\n    },\n    {\n        \"WbsCode\": \"TIM00181-01-0Y-01-1000\",\n        \"WbsDescritption\": \"4-Data and Analytics Modernizati-\",\n        \"CompanyCustomerName\": \"TIM SPA\",\n        \"ProfitCenter\": \"0380444601\",\n        \"EngagementPartner\": \"Caio Maionchi\",\n        \"Closed\": \"\",\n        \"IsExpensesBlocked\": \"\",\n        \"ServiceLineCode\": \"0380444601\",\n        \"PARNR\": \"06001472\",\n        \"PartecipatingPartner (nome e cognome del partecipating partner)\": null,\n        \"PARN PartecipatingPartner\": null\n    },\n    {\n        \"WbsCode\": \"TIM00181-01-1H-01-1000\",\n        \"WbsDescritption\": \"8-Data and Analytics Modernizati-\",\n        \"CompanyCustomerName\": \"TIM SPA\",\n        \"ProfitCenter\": \"0380444601\",\n        \"EngagementPartner\": \"Caio Maionchi\",\n        \"Closed\": \"\",\n        \"IsExpensesBlocked\": \"\",\n        \"ServiceLineCode\": \"0380444601\",\n        \"PARNR\": \"06001472\",\n        \"PartecipatingPartner (nome e cognome del partecipating partner)\": null,\n        \"PARN PartecipatingPartner\": null\n    },\n    {\n        \"WbsCode\": \"AAA00079-01-02-01-1000\",\n        \"WbsDescritption\": \"Sviluppo nuovi report - Power BI\",\n        \"CompanyCustomerName\": \"A2A AMBIENTE SPA\",\n        \"ProfitCenter\": \"0380444201\",\n        \"EngagementPartner\": \"Mario Berettaa\",\n        \"Closed\": \"\",\n        \"IsExpensesBlocked\": \"\",\n        \"ServiceLineCode\": \"0380444201\",\n        \"PARNR\": \"06001472\",\n        \"PartecipatingPartner (nome e cognome del partecipating partner)\": null,\n        \"PARN PartecipatingPartner\": null\n    }\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8888/coc", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["coc"]}}, "response": []}, {"name": "Report Advisor", "request": {"method": "GET", "header": [{"key": "coc", "value": "", "type": "text"}, {"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/reports/:company_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["reports", ":company_id", ":document_type"], "variable": [{"key": "company_id", "value": null}, {"key": "document_type", "value": "RptAdvisor"}]}}, "response": []}, {"name": "Report Expert", "request": {"method": "GET", "header": [{"key": "coc", "value": "", "type": "text"}, {"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/reports/:company_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["reports", ":company_id", ":document_type"], "variable": [{"key": "company_id", "value": null}, {"key": "document_type", "value": "RptExpert"}]}}, "response": []}, {"name": "Legal Procedures Deed", "request": {"method": "GET", "header": [{"key": "coc", "value": "", "type": "text"}, {"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/legal_procedures_deed/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["legal_procedures_deed", ":company_id"], "variable": [{"key": "company_id", "value": null}]}}, "response": []}, {"name": "Company Card", "request": {"method": "GET", "header": [{"key": "Id", "value": "<EMAIL>", "type": "text"}, {"key": "coc", "value": "dwdwdwdwdw", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/company_card/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["company_card", ":company_id"], "variable": [{"key": "company_id", "value": "bf397702-3a62-4322-b31b-a6fcc86de036"}]}}, "response": []}, {"name": "List of Balances", "request": {"method": "GET", "header": [{"key": "Id", "value": "4974b109-d8cf-4559-9971-04b079396159", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/balances/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["balances", ":company_id"], "variable": [{"key": "company_id", "value": ""}]}}, "response": []}, {"name": "Balance", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/balance/:company_id/document/:document_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["balance", ":company_id", "document", ":document_id"], "variable": [{"key": "company_id", "value": ""}, {"key": "document_id", "value": ""}]}}, "response": []}, {"name": "List of Finacial Statement", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/financial_statements/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["financial_statements", ":company_id"], "variable": [{"key": "company_id", "value": null}]}}, "response": []}, {"name": "Financial Statement", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/financial_statement/:company_id/document/:document_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["financial_statement", ":company_id", "document", ":document_id"], "variable": [{"key": "company_id", "value": null}, {"key": "document_id", "value": null}]}}, "response": []}, {"name": "Shareholders", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/shareholders/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["shareholders", ":company_id"], "variable": [{"key": "company_id", "value": null}]}}, "response": []}, {"name": "Shares", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/shares/:company_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["shares", ":company_id"], "variable": [{"key": "company_id", "value": null}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/visura/:company_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["visura", ":company_id", ":document_type"], "variable": [{"key": "company_id", "value": ""}, {"key": "document_type", "value": "VATTUPDF"}]}}, "response": []}, {"name": "Visura History", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/visura/:company_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["visura", ":company_id", ":document_type"], "variable": [{"key": "company_id", "value": null}, {"key": "document_type", "value": "VSTORPDF"}]}}, "response": []}, {"name": "Person", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/people/:tax_code", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["people", ":tax_code"], "variable": [{"key": "tax_code", "value": "CVRSRN73M47F205Z"}]}}, "response": []}, {"name": "Person Report Standard", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/person_report/:person_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["person_report", ":person_id", ":document_type"], "variable": [{"key": "person_id", "value": ""}, {"key": "document_type", "value": "RptStandard"}]}}, "response": []}, {"name": "Person Report Complete", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/person_report/:person_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["person_report", ":person_id", ":document_type"], "variable": [{"key": "person_id", "value": ""}, {"key": "document_type", "value": "RptComplete"}]}}, "response": []}, {"name": "Representative Card Historical", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/representative_card/:person_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["representative_card", ":person_id", ":document_type"], "variable": [{"key": "person_id", "value": ""}, {"key": "document_type", "value": "SKS"}]}}, "response": []}, {"name": "Representative Card Current", "request": {"method": "GET", "header": [{"key": "user_id", "value": "consumer_id", "type": "text"}, {"key": "coc_id", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/representative_card/:person_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["representative_card", ":person_id", ":document_type"], "variable": [{"key": "person_id", "value": null}, {"key": "document_type", "value": "SKP"}]}}, "response": []}, {"name": "Representative Card Complete", "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/representative_card/:person_id/:document_type", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["representative_card", ":person_id", ":document_type"], "variable": [{"key": "person_id", "value": null}, {"key": "document_type", "value": "SKC"}]}}, "response": []}, {"name": "All Approver Requests", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8888/requests", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["requests"]}}, "response": []}, {"name": "All Consumer Requests", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8888/requests", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["requests"]}}, "response": []}, {"name": "Single Request by ID (Polling case)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://127.0.0.1:8888/requests?request_id={{$guid}}", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["requests"], "query": [{"key": "request_id", "value": "{{$guid}}"}]}}, "response": []}, {"name": "Put Purchase Request of Shares", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"shares\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"shares\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request of LegalProceduresDeed", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"legal_procedures_deed\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"legal_procedures_deed\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request of RptStandard", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"person_report\",\r\n        \"method_params\": {\r\n            \"person_id\": \"...\",\r\n            \"document_type\": \"RptStandard\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "person"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request of Visura", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"visura\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"VSTORPDF\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request of Financial Statement", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"financial_statement\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"financial_statement\",\r\n            \"document_id\": \"...\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request of Balance", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"balance\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"balance\",\r\n            \"document_id\": \"...\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request Rebuy Shares", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"shares\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"shares\",\r\n            \"document_id\": \"...\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\",\r\n    \"action\": \"rebuy\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request Rebuy Balance", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"balance\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"balance\",\r\n            \"document_id\": \"...\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\",\r\n    \"action\": \"rebuy\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Put Purchase Request Rebuy Visura", "request": {"method": "PUT", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"service_request_info\": {\r\n        \"service_type\": \"visura\",\r\n        \"method_params\": {\r\n            \"company_id\": \"...\",\r\n            \"document_type\": \"VSTORPDF\",\r\n            \"document_id\": \"...\"\r\n            }\r\n        },\r\n    \"coc\": \"...\",\r\n    \"requested_at\": \"...\",\r\n    \"action\": \"rebuy\"\r\n}"}, "url": {"raw": "http://127.0.0.1:8888/request/:entity_type/:entity_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":entity_type", ":entity_id"], "variable": [{"key": "entity_type", "value": "company"}, {"key": "entity_id", "value": ""}]}}, "response": []}, {"name": "Post Approving Request", "request": {"method": "POST", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\"service_request_info\": {\r\n    \"approve\": true\r\n}}"}, "url": {"raw": "http://127.0.0.1:8888/request/:request_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":request_id"], "variable": [{"key": "request_id", "value": ""}]}}, "response": []}, {"name": "Post Rejecting Request", "request": {"method": "POST", "header": [{"key": "Id", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\"service_request_info\": {\r\n    \"approve\": false\r\n}}"}, "url": {"raw": "http://127.0.0.1:8888/request/:request_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["request", ":request_id"], "variable": [{"key": "request_id", "value": ""}]}}, "response": []}, {"name": "Post Rebuy Document", "request": {"method": "PATCH", "header": [{"key": "Id", "value": "", "type": "text"}, {"key": "coc", "value": "", "type": "text"}], "url": {"raw": "http://127.0.0.1:8888/rebuy/:document_id", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8888", "path": ["rebuy", ":document_id"], "variable": [{"key": "document_id", "value": ""}]}}, "response": []}], "auth": {"type": "oauth2", "oauth2": [{"key": "tokenName", "value": "Azure Entra Id token", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/{{tenantId}}/oauth2/v2.0/authorize", "type": "string"}, {"key": "scope", "value": "openid profile email Directory.AccessAsUser.All Directory.Read.All Mail.Read offline_access User.Read.All User.Read User.ReadBasic.All", "type": "string"}, {"key": "clientSecret", "value": "{{clientSecret}}", "type": "string"}, {"key": "clientId", "value": "{{clientId}}", "type": "string"}, {"key": "redirect_uri", "value": "https://it-fvfhf0fdq05p.atrema.deloitte.com:8080/reply", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "clientID", "value": "da8be74d-b91e-4fbf-bc6f-8ed107aa5b5d"}, {"key": "clientSecret", "value": "****************************************"}, {"key": "tenantId", "value": "36da45f1-dd2c-4d1f-af13-5abe46b99921", "type": "string"}]}