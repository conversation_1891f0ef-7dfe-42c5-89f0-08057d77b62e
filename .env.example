# Environment
CLIENT_URL=""
CLIENT_URL_LOC=""
COC_PROVIDER_URL=""
DEBUG=""
ENVIRONMENT=""
FLASK_APP=""
FLASK_ENV=""
TIMEZONE=""
TZ=""

# Database
SECRET_KEY=""
SQLALCHEMY_DATABASE_URI=""
SQLALCHEMY_TRACK_MODIFICATIONS=""

# Seeding
ADMIN_USERID=""
APPROVER_USERID=""
CONSUMER_USERID=""
DB_ADMIN_ID=""
DB_APPROVER_ID=""
DB_CONSUMER2_ID=""
DB_CONSUMER_ID=""
TEST_USERS_EMPLOYEE_ID=""

# Data Supplier
BALANCE_ROOT=""
CERTIFICATE_NAME=""
COMPANYCARD_ROOT=""
COMPANY_ROOT=""
DATA_SUPPLIER_PASSWORD=
DATA_SUPPLIER_USERNAME=""
FINANCIALSTATEMENT_ROOT=""
LEGALPROCEDURES_ROOT=""
PERSONREPORT_ROOT=""
PERSON_ROOT=""
REPORT_ROOT=""
REPRESENTATIVECARD_ROOT=""
SHAREHOLDERS_ROOT=""
SHARES_ROOT=""
SUPPLIER_BASE_URL=
VISURA_ROOT=""

# Auth
APPLICATIONINSIGHTS_CONNECTION_STRING=""
AZURE_CLIENT_ID=""
AZURE_CLIENT_SECRET=""
AZURE_SCOPE=""
AZURE_STORAGE_CONN_STRING=""
AZURE_STORAGE_IMPORT_CONTAINER_NAME=""
AZURE_STORAGE_IMPORT_FOLDER_NAME=""
AZURE_STORAGE_URL=""
AZURE_TENANT_ID=""
GRAPH_URL=""
ISSUER_URL=""
JWKS_URL=""

# Cache
CACHE_DEFAULT_TIMEOUT=""
CACHE_HOST=""
CACHE_LOCAL_HOST=""
CACHE_LOCAL_PASSWORD=""
CACHE_LOCAL_SCOPE=""
CACHE_PASSWORD=""
CACHE_POOL_MAX_CONNECTIONS=""
CACHE_PORT=""
CACHE_SCOPE=""
CACHE_SOCKET_CONNECT_TIMEOUT=""
CACHE_SOCKET_TIMEOUT=""

# Server
# Custom Gunicorn config using SERVER_* environment variables (instead of standard GUNICORN_*)
SERVER_BIND=""
SERVER_LOG_LEVEL=""
SERVER_MAX_WORKERS=""
SERVER_PRELOAD_APP=""
SERVER_PROC_NAME=""
SERVER_RELOAD=""
SERVER_TIMEOUT=""
SERVER_WEB_CONCURRENCY=""
SERVER_WORKERS_PER_CORE=""

# Mail and users
ADMIN_EMAIL=""
ADMIN_NAME=""
ADMIN_SURNAME=""
APPROVER_EMAIL=""
APPROVER_NAME=""
APPROVER_SURNAME=""
CONSUMER_EMAIL=""
CONSUMER_NAME=""
CONSUMER_SURNAME=""
MAIL_DEFAULT_SENDER=""
MAIL_PORT=""
MAIL_SERVER=""
