#!/usr/bin/env python3
"""
Test script per verificare che Flask si blocchi quando la cache fallisce.

Questo script simula un fallimento della cache impostando variabili d'ambiente
non valide e verifica che l'applicazione termini correttamente.
"""

import os
import sys
import subprocess
import tempfile

def test_cache_failure_termination():
    """
    Testa che Flask termini quando la cache fallisce.
    """
    print("🧪 Testing Flask termination on cache failure...")
    
    # Crea un file temporaneo per testare l'import della cache
    test_script = """
import os
import sys

# Imposta variabili d'ambiente non valide per simulare il fallimento della cache
os.environ['CACHE_HOST'] = 'invalid-host-that-does-not-exist'
os.environ['CACHE_PORT'] = '9999'
os.environ['CACHE_PASSWORD'] = 'invalid-password'
os.environ['CACHE_POOL_MAX_CONNECTIONS'] = '10'
os.environ['CACHE_SOCKET_CONNECT_TIMEOUT'] = '5'
os.environ['CACHE_SOCKET_TIMEOUT'] = '5'

try:
    # Questo dovrebbe terminare il processo con sys.exit(1)
    from app.cache import cache
    print("❌ ERROR: Cache initialization should have failed!")
    sys.exit(0)  # Se arriviamo qui, il test è fallito
except SystemExit as e:
    if e.code == 1:
        print("✅ SUCCESS: Flask correctly terminated on cache failure!")
        sys.exit(0)  # Test passato
    else:
        print(f"❌ ERROR: Unexpected exit code: {e.code}")
        sys.exit(1)
except Exception as e:
    print(f"❌ ERROR: Unexpected exception: {e}")
    sys.exit(1)
"""
    
    # Scrivi il test script in un file temporaneo
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script)
        temp_file = f.name
    
    try:
        # Esegui il test script
        result = subprocess.run([sys.executable, temp_file], 
                              capture_output=True, text=True, timeout=30)
        
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
            
        if result.returncode == 0:
            print("🎉 Test PASSED: Flask termination works correctly!")
        else:
            print("❌ Test FAILED: Flask did not terminate as expected")
            
    except subprocess.TimeoutExpired:
        print("⏰ Test TIMEOUT: The process did not terminate within 30 seconds")
    except Exception as e:
        print(f"❌ Test ERROR: {e}")
    finally:
        # Pulisci il file temporaneo
        try:
            os.unlink(temp_file)
        except:
            pass

if __name__ == "__main__":
    test_cache_failure_termination()
