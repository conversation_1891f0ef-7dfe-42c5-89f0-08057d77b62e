#!/bin/bash

# =============================================================================
#                               DEPLOY SCRIPT
# =============================================================================
#
# Description:
# This script automates the deployment process for your dockerized application.
# It includes the steps to check and start the Docker daemon, build and deploy Docker containers,
# and manage the application lifecycle.
# It is designed to streamline the deployment process and ensure that all necessary steps are completed in the correct order.
# It logs you into Azure and into the Azure container registry.
# If some items are not ready it waits for their completion.
#
# You only need to have the sudo privileges already enabled.
#
# Table of Contents:
# 1. Check and start Docker Daemon and the Desktop application
# 2. Build Docker Image
# 3. Azure login
# 4. Azure ACR login
# 5. Tag image for Azure ACR
# 6. Push to Azure ACR
# 7. Stop Docker Containers, Daemon, and Application
# 8. Launch app monitoring
#
# =============================================================================

# Define color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Function to check if a specific string is in the output of a command
contains() {
    [[ $1 == *$2* ]]
}

# Function to print error messages
print_error() {
    echo -e "${RED}${BOLD}ERROR:${NC} $1"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}${BOLD}SUCCESS:${NC} $1"
}

# Function to print info messages
print_info() {
    echo -e "${YELLOW}${BOLD}INFO:${NC} $1"
}

# Separator
separator() {
    echo -e "${YELLOW}------------------------------------------------${NC}"
}

# Check for --tag parameter
if [[ -z "$1" || "$1" != "--tag" || -z "$2" ]]; then
    print_error "The --tag parameter is required. Usage: $0 --tag <version>"
    exit 1
fi

# Extract the tag value
IMAGE_TAG="$2"
print_info "Using image tag: $IMAGE_TAG"

separator

# Step 1: Start Docker Daemon and Application
echo "Starting Docker daemon and application..."

# Check if running on a system with systemctl
if command -v systemctl > /dev/null 2>&1; then
    echo "Using systemctl to start Docker..."
    systemctl start docker || { echo "Failed to start Docker daemon. Exiting..."; exit 1; }
elif command -v service > /dev/null 2>&1; then
    echo "Using service command to start Docker..."
    service docker start || { echo "Failed to start Docker daemon. Exiting..."; exit 1; }
elif [ "$(uname)" == "Darwin" ]; then
    echo "Detected macOS. Starting Docker Desktop..."
    open --background -a Docker || { echo "Failed to start Docker Desktop. Exiting..."; exit 1; }
else
    echo "ERROR: Docker daemon control method not found. Exiting..."
    exit 1
fi

# Wait for Docker to start
echo "Waiting for Docker to start..."
sleep 10

# Check if Docker is running
docker info > /dev/null 2>&1 || { echo "Docker is not running. Exiting..."; exit 1; }

echo "Docker daemon and application started successfully."

separator

# Step 2: Build image
print_info "Building Docker image for API..."
IMAGE_TAG_SANITIZED=$(echo "$IMAGE_TAG" | sed 's|/|_|g')
print_info "Using sanitized image tag: $IMAGE_TAG_SANITIZED"
docker build --tag api:$IMAGE_TAG_SANITIZED --target production .
if [ $? -ne 0 ]; then
    print_error "Docker image build for API failed. Exiting."
    exit 1
fi
print_success "Docker image for API built successfully."

separator

# Step 3: Azure login
print_info "Checking Azure login status..."
az_account_output=$(az account list 2>&1)
if contains "$az_account_output" "<EMAIL>"; then
    print_success "Already logged into Azure."
else
    print_info "Logging into Azure..."
    az login
    if [ $? -ne 0 ]; then
        print_error "Azure login failed. Exiting."
        exit 1
    fi
fi

separator

# Step 4: Azure ACR login
print_info "Logging into Azure ACR..."
if ! az acr login -n spartaxtest; then
    print_error "Azure ACR login failed. Exiting."
    exit 1
fi
print_success "Azure ACR login successful."

separator

# Step 5: Tag image for Azure ACR
print_info "Tagging Docker image for Azure ACR..."
docker tag api:$IMAGE_TAG_SANITIZED spartaxtest.azurecr.io/api:$IMAGE_TAG_SANITIZED
if [ $? -ne 0 ]; then
    print_error "Docker image tagging for Azure ACR failed. Exiting."
    exit 1
fi
print_success "Docker image tagged for Azure ACR successfully."

separator

# Step 6: Push to Azure ACR
print_info "Pushing image to Azure ACR..."
docker push spartaxtest.azurecr.io/api:$IMAGE_TAG_SANITIZED
if [ $? -ne 0 ]; then
    print_error "Docker image push to Azure ACR failed. Exiting."
    exit 1
fi
print_success "Docker image pushed to Azure ACR successfully."

separator

# Step 7: Stop Docker Containers, Daemon, and Application
echo "Stopping Docker containers and application..."

# Stop Docker daemon
echo "Stopping Docker daemon..."

# Check if running on a system with systemctl
if command -v systemctl > /dev/null 2>&1; then
    echo "Using systemctl to stop Docker..."
    sudo systemctl stop docker || { echo "Failed to stop Docker daemon. Exiting..."; exit 1; }
elif [ "$(uname)" == "Darwin" ]; then
    echo "Detected macOS. Closing Docker Desktop..."
    osascript -e 'quit app "Docker Desktop"' || { echo "Failed to stop Docker Desktop. Exiting..."; exit 1; }
else
    echo "ERROR: Docker daemon control method not found. Exiting..."
    exit 1
fi

separator

# Step 8: Launch app monitoring
print_info "Launching app monitoring for API..."
az webapp log tail --name spartaxtest-auth --resource-group IT-RG-TAX-NPD-EUN-0000382-AppService
if [ $? -ne 0 ]; then
    print_error "App monitoring for API launch failed. Exiting."
    exit 1
fi
print_success "App monitoring for API launched successfully."

separator

print_success "API deployment script executed successfully."
