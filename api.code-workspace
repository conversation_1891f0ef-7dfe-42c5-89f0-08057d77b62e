{
	"folders": [
		{
			"path": "."
		}
	],
	"extensions": {
		"recommendations": [
			"ms-vscode-remote.remote-containers",
			"ms-azuretools.vscode-docker",
			"ms-azure-devops.azure-pipelines",
			"ms-azuretools.vscode-azureappservice",
			"ms-azuretools.vscode-azurestorage",
			"ms-vscode-remote.vscode-remote-extensionpack",
			"ms-python.black-formatter"
		]
	},
	"settings": {
		"appService.defaultWebAppToDeploy": "/subscriptions/c49a0022-6de6-43a4-914c-032a1b2bc532/resourceGroups/IT-RG-TAX-NPD-EUN-0000382-AppService/providers/Microsoft.Web/sites/spartaxtest-auth",
		"appService.deploySubpath": ".",
		"git.autofetch": true,
		"files.exclude": {
			"**/__pycache__": true,
			"**/.venv": true,
			"**/.pytest_cache": true,
		},
		"python.testing.pytestArgs": [
			"tests"
		],
		"python.testing.pytestEnabled": true,
		"python.testing.unittestEnabled": false,
		"[python]": {
			"editor.formatOnSave": true,
			"editor.formatOnSaveMode": "file",
			"editor.formatOnPaste": false,
			"editor.insertSpaces": true,
			"editor.defaultFormatter": "ms-python.black-formatter",
		},
		"python.terminal.activateEnvInCurrentTerminal": true,
		"python.terminal.activateEnvironment": true,
		"terminal.integrated.defaultProfile.windows": "Windows PowerShell",
	},
	"tasks": {
		"version": "2.0.0",
		"tasks": [
			{
				"label": "killdebugger",
				"type": "shell",
				"command": "lsof -t -i tcp:8888 | xargs kill -9",
				"presentation": {
					"showReuseMessage": false,
					"close": true
				},
			}
		]
	},
	"launch": {
		"version": "0.2.0",
		"configurations": [
			{
				"name": "Flask application",
				"cwd": "${workspaceFolder}",
				"console": "integratedTerminal",
				"type": "debugpy",
				"module": "flask",
				"request": "launch",
				"subProcess": true,
				"args": [
					"run",
					"--port",
					"8888",
					"--host",
					"0.0.0.0"
				],
				"env": {
					"FLASK_APP": "app.main:app",
					"FLASK_ENV": "development",
					"FLASK_DEBUG": "1",
				},
				"justMyCode": false
			},
			{
				"name": "Gunicorn server",
				"cwd": "${workspaceFolder}",
				"console": "integratedTerminal",
				"type": "python",
				"program": "${workspaceFolder}/.venv/bin/python",
				"request": "launch",
				"args": [
					"-m",
					"poetry",
					"run",
					"gunicorn",
					"-c",
					"gunicorn.config.py",
					"app.main:app",
				],
				"postDebugTask": "killdebugger",
			}
		],
	}
}
