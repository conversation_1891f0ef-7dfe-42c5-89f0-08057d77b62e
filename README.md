[![Python 3.10.12](https://img.shields.io/badge/python-3.10.12-blue.svg)](https://www.python.org/downloads/release/python-31012/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

Spartax API is the backend for the SPartax application. It's dockerized multistage restful API.

- [Requirements](#requirements)
- [Project structure](#project-structure)
- [Endpoints and parameters](#endpoints-and-parameters)
___
Depth, documentation can be found in the [docs](./docs) folder. The [INSTALL.md](./docs/INSTALL.md) file contains the installation instructions and the [DEVELOPMENT.md](./docs/DEVELOPMENT.md) file contains the development instructions.
___

### Requirements

- Python (3.9.21)
- poetry (1.2.1)
- Docker (20.10.17)
- docker-compose (2.10.2)

### Project structure

```bash
├── .vscode
│   ├── extensions.json
│   ├── launch.json
│   └── tasks.json
├── app
│   ├── assets
│       ├── certs                         # local development certs
│       ├── images
│   ├── migrations                        # alembic migrations
│   ├── models
│       ├── company.py
│       ├── person.py
│       ├── request.py
│       ├── user.py
│   ├── resources
│       ├── balance.py
│       ├── company.py
│       ├── company_card.py
│       ├── document.py
│       ├── financial_statement.py
│       ├── health.py
│       ├── legal_procedures_deed.py
│       ├── shareholders.py
│       ├── shares.py
│       ├── person_report.py
│       ├── person.py
│       ├── report.py
│       ├── representative_card.py
│       ├── user.py
│       ├── visura.py
│   ├── config.py                         # app config
│   ├── extension.py                      # app extensions
│   ├── main.py                           # app entry point
│   ├── utils.py                          # app utils
├── certs                                 # certificates folder (autogenerated)
├── docs                                  # documentation
│   ├── DEVELOPMENT.md
│   ├── INSTALL.md
├── logs                                 # logs folder
├── migrations                           # Alembic migrations folder
├── seeds                                # seeding instructions and data
├── temporary                            # folder for local downloaded files
├── tests                                # tests folder
├── .devcontainer.json                   # Dev container configuration
├── .dockerignore                        # Docker ignore (should be at root level)
├── .env.example                         # .env template for all necessary env variables
├── .gitignore                           # gitignore
└── .pre-commit-config.yaml              # pre-commit config
└── api.code-workspace                   # vscode workspace
└── azure-pipelines.yaml                 # azure pipelines config
├── deploy.sh                            # deploy script
├── docker-entrypoint.sh                 # Docker entrypoint file
├── Dockerfile
└── gunicorn.config.py                   # gunicorn config
└── postman.json                         # postman collection
└── pyproject.toml                       # poetry config
└── README.md
```

### Endpoints and parameters
All endpoints available through the application and their accepted parameters are stored inside [postman.json](./postman.json).
