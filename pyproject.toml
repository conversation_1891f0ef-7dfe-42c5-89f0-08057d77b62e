[tool.poetry]
name = "spartax-api"
version = "0.1.0"
description = "API backend for the internal application Spartax"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <og<PERSON><PERSON>@deloitte.it>"]
readme = "README.md"
homepage = "https://spartax.deloitte.it"
repository = "https://dcmgithub.deloitte.it/DCM-IT-TAX-DEV/spartax-api"

[tool.poetry.dependencies]
alembic = "^1.13.2"
azure-core = "^1.26.3"
azure-identity = "^1.12.0"
azure-monitor-opentelemetry = "^1.6.1"
azure-storage-blob = "^12.15.0"
bandit = "^1.7.4"
black = "22.3.0"
coverage = { version = ">=5.2.1", extras = ["toml"]}
cryptography = "^37.0.4"
flake8 = "4.0.1"
Flask = "2.3.2"
flask-limiter = "^3.3.1"
flask-mail = "^0.9.1"
Flask-Migrate = "3.1.0"
Flask-RESTful = "0.3.9"
flask-restplus = "^0.13.0"
flask-seeder = "^1.2.0"
Flask-SQLAlchemy = "3.1"
greenlet = "^3"
gunicorn = "^20.1.0"
loguru = "^0.7.2"
markdown = "^3.6"
marshmallow-sqlalchemy = "^0.28.1"
msal = "^1.18.0"
mypy = "0.950"
names = "^0.3.0"
opencensus-ext-azure = "^1.1.9"
pre-commit = "^2.20.0"
psycopg2-binary = "^2.9.4"
PyJWT = "^2.4.0"
pyOpenSSL = "^22.0.0"
pyperclip = "^1.9.0"
pytest = "7.1.2"
pytest-cov = "^3.0.0"
python = ">=3.10,<3.13"
python-dotenv = "^0.21.0"
pyvat = "^1.3.15"
redis = "^5.0.8"
SQLAlchemy-Utils = "^0.38.2"
uvicorn = "^0.29.0"
Werkzeug = "^2.2.2"
xmltodict = "0.13.0"
zeep = "4.1.0"
opencensus-ext-flask = "^0.8.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[project]
name = "spartax-api"
requires-python = ">=3.10,<3.13"
