# From: https://github.com/tiangolo/uvicorn-gunicorn-docker/blob/315f04413114e938ff37a410b5979126facc90af/python3.7/SERVER_conf.py
import json
import multiprocessing
import os

if os.getenv("WEBSITE_SITE_NAME") is None:
    try:
        from dotenv import load_dotenv

        load_dotenv()
    except ImportError:
        pass

reuse_port = True
workers_per_core_str = os.getenv("SERVER_WORKERS_PER_CORE")
web_concurrency_str = os.getenv("SERVER_WEB_CONCURRENCY")
host = os.getenv("HOST")
port = os.getenv("PORT")
proc_name = os.getenv("SERVER_PROC_NAME")
use_bind = f"{host}:{port}"
use_loglevel = os.getenv("SERVER_LOG_LEVEL")
reload = os.getenv("SERVER_RELOAD")
cores = multiprocessing.cpu_count()
timeout = 1000
workers_per_core = float(workers_per_core_str)
default_web_concurrency = workers_per_core * cores
if web_concurrency_str:
    web_concurrency = int(web_concurrency_str)
    assert web_concurrency > 0
else:
    web_concurrency = max(int(default_web_concurrency), 2)

# Gunicorn config variables
loglevel = use_loglevel
workers = web_concurrency
bind = use_bind
keepalive = 120
errorlog = "-"
accesslog = "-"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# For debugging and testing
log_data = {
    "loglevel": loglevel,
    "workers": workers,
    "bind": bind,
    # Additional, non-gunicorn variables
    "workers_per_core": workers_per_core,
    "host": host,
    "port": port,
}

print(json.dumps(log_data))
